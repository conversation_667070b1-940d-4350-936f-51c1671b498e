// components/task-list/task-list.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    tasks: {
      type: Array,
      value: [],
      observer: function(newVal) {
        this.updateTaskDisplay(newVal);
      }
    },
    showProgress: {
      type: Boolean,
      value: true
    },
    allowToggle: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    displayTasks: [],
    completedCount: 0,
    totalCount: 0,
    progressPercentage: 0,
    showRewardAnimation: false,
    rewardText: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新任务显示
     */
    updateTaskDisplay(tasks) {
      if (!tasks || !Array.isArray(tasks)) {
        return;
      }

      const completedCount = tasks.filter(task => task.completed).length;
      const totalCount = tasks.length;
      const progressPercentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

      this.setData({
        displayTasks: tasks,
        completedCount: completedCount,
        totalCount: totalCount,
        progressPercentage: progressPercentage
      });
    },

    /**
     * 任务点击处理
     */
    onTaskTap(e) {
      if (!this.data.allowToggle) {
        return;
      }

      const { index } = e.currentTarget.dataset;
      const task = this.data.displayTasks[index];
      
      if (!task || task.completed) {
        return;
      }

      // 播放完成动画
      this.playTaskCompleteAnimation(index);
      
      // 更新任务状态
      const updatedTasks = [...this.data.displayTasks];
      updatedTasks[index] = {
        ...task,
        completed: true,
        completedAt: new Date()
      };

      // 显示奖励提示
      this.showRewardToast(task.reward);

      // 触发任务完成事件
      this.triggerEvent('taskComplete', {
        taskId: task.id,
        task: updatedTasks[index],
        reward: task.reward,
        allTasks: updatedTasks
      });

      // 更新显示
      this.updateTaskDisplay(updatedTasks);
    },

    /**
     * 播放任务完成动画
     */
    playTaskCompleteAnimation(index) {
      const animationClass = `task-complete-${index}`;
      this.setData({
        [`displayTasks[${index}].animationClass`]: 'task-completing'
      });

      setTimeout(() => {
        this.setData({
          [`displayTasks[${index}].animationClass`]: 'task-completed'
        });
      }, 300);
    },

    /**
     * 显示奖励提示
     */
    showRewardToast(reward) {
      if (reward && reward > 0) {
        this.setData({
          showRewardAnimation: true,
          rewardText: `+${reward} 经验值`
        });

        setTimeout(() => {
          this.setData({
            showRewardAnimation: false
          });
        }, 2000);

        wx.showToast({
          title: `获得 ${reward} 经验值！`,
          icon: 'success',
          duration: 1500
        });
      }
    },

    /**
     * 获取任务图标
     */
    getTaskIcon(taskType) {
      const iconMap = {
        'walk': '/images/icons/walk.png',
        'water': '/images/icons/water.png',
        'sleep': '/images/icons/sleep.png',
        'exercise': '/images/icons/exercise.png',
        'feed': '/images/icons/feed.png',
        'play': '/images/icons/play.png'
      };
      
      return iconMap[taskType] || '/images/icons/task.png';
    },

    /**
     * 格式化任务描述
     */
    formatTaskDescription(task) {
      if (task.progress && task.target) {
        return `${task.description} (${task.progress}/${task.target})`;
      }
      return task.description;
    },

    /**
     * 重置所有任务
     */
    resetAllTasks() {
      const resetTasks = this.data.displayTasks.map(task => ({
        ...task,
        completed: false,
        progress: 0,
        completedAt: null,
        animationClass: ''
      }));

      this.updateTaskDisplay(resetTasks);
      
      this.triggerEvent('tasksReset', {
        tasks: resetTasks
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      if (this.data.tasks && this.data.tasks.length > 0) {
        this.updateTaskDisplay(this.data.tasks);
      }
    }
  }
});