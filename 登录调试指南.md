# 登录调试指南

## 问题分析

如果获取到的还是"微信用户"，可能的原因：

1. **开发工具模拟问题**：开发工具可能不会返回真实用户信息
2. **基础库版本问题**：需要基础库 >= 2.10.4
3. **调用时机问题**：必须在用户点击事件中直接调用
4. **小程序状态问题**：需要在真机上测试

## 测试步骤

### 1. 使用测试页面
1. 编译小程序
2. 导航到 `pages/test-login/test-login` 页面
3. 点击"测试 getUserProfile"按钮
4. 查看控制台日志和页面显示

### 2. 检查控制台日志
应该看到类似的日志：
```
getUserProfile 成功: {userInfo: {...}, ...}
用户信息: {nickName: "真实昵称", avatarUrl: "真实头像URL", ...}
昵称: 真实昵称
头像: https://thirdwx.qlogo.cn/...
```

### 3. 真机测试
1. 点击微信开发者工具的"预览"按钮
2. 用微信扫码在真机上测试
3. 真机上的表现可能与开发工具不同

## 修复方案

### 1. 直接调用方式（已实现）
现在个人设置页面使用直接调用方式：
```javascript
// 直接在点击事件中调用
wx.getUserProfile({
  desc: '用于完善用户资料',
  success: (profileRes) => {
    console.log('用户信息:', profileRes.userInfo);
    // 继续处理...
  }
});
```

### 2. 检查基础库版本
确保 `project.config.json` 中：
```json
"libVersion": "2.19.4"
```

### 3. 检查小程序设置
在微信开发者工具中：
1. 点击"详情"
2. 确保"不校验合法域名"已勾选
3. 确保基础库版本正确

## 常见问题

### Q: 开发工具中显示"微信用户"？
A: 这是正常的，开发工具可能不返回真实信息，请在真机上测试。

### Q: 真机上也显示"微信用户"？
A: 检查小程序是否已发布，某些功能需要小程序上线后才能正常工作。

### Q: 授权弹窗不出现？
A: 确保在用户点击事件中直接调用，不能在异步回调中调用。

### Q: 云函数调用失败？
A: 检查云函数是否正确部署，环境ID是否正确。

## 调试命令

在控制台中可以直接测试：
```javascript
// 测试 getUserProfile
wx.getUserProfile({
  desc: '测试',
  success: res => console.log('成功:', res.userInfo),
  fail: err => console.log('失败:', err)
});
```

## 预期结果

成功时应该看到：
- 真实的微信昵称
- 真实的微信头像URL
- 用户的性别、地区等信息
- 控制台中的详细日志

如果仍然显示"微信用户"，请在真机上测试或检查小程序发布状态。