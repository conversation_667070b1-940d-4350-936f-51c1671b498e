/* pages/profile/index.wxss */
page {
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  min-height: 100vh;
  transition: all 0.3s ease;
}

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  padding-bottom: 40rpx; /* 调整底部间距与首页保持一致 */
  transition: all 0.3s ease;
}

.page-container.dark-mode {
  background: linear-gradient(135deg, #1f2937 0%, #581c87 50%, #1f2937 100%);
}

/* Header */
.header {
  padding: 8rpx 40rpx 0;
  height: 88rpx;
  display: flex;
  align-items: flex-start;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
}

.dark-mode .header-title {
  color: #ffffff;
}

.dark-mode-toggle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.dark-mode .dark-mode-toggle {
  background: rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.5);
}

.toggle-icon {
  font-size: 32rpx;
}

/* Profile Card */
.profile-card {
  margin: 0 40rpx 20rpx; /* 调整间距与首页保持一致 */
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 60rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.dark-mode .profile-card {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  gap: 30rpx;
}

.avatar-container {
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
}

/* 头像图片样式 */
image.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: none;
}

.dark-mode .avatar {
  background: linear-gradient(135deg, #7c3aed, #ec4899);
}

.edit-button {
  position: absolute;
  bottom: -5rpx;
  right: -5rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #f97316;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  font-size: 20rpx;
  color: white;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.dark-mode .username {
  color: #ffffff;
}

.user-level {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.crown-icon {
  font-size: 24rpx;
}

.level-text {
  font-size: 24rpx;
  color: #666;
}

.dark-mode .level-text {
  color: #d1d5db;
}

.login-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 5rpx;
}

.dark-mode .login-time {
  color: #9ca3af;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.companion-days {
  color: #8b5cf6;
}

.completed-tasks {
  color: #3b82f6;
}

.total-exp {
  color: #10b981;
}

.dark-mode .companion-days {
  color: #a78bfa;
}

.dark-mode .completed-tasks {
  color: #60a5fa;
}

.dark-mode .total-exp {
  color: #34d399;
}

.stat-label {
  font-size: 20rpx;
  color: #666;
}

.dark-mode .stat-label {
  color: #9ca3af;
}

/* Achievements */
.achievements {
  display: flex;
  justify-content: center;
  gap: 15rpx;
}

.achievement-badge {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.achievement-badge.unlocked {
  background: linear-gradient(135deg, #fbbf24, #f97316);
}

.achievement-badge.locked {
  background: #e5e7eb;
}

.dark-mode .achievement-badge.locked {
  background: #374151;
}

/* Pet Card */
.pet-card {
  margin: 10rpx 40rpx 20rpx; /* 调整底部间距与首页保持一致 */
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 40rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.dark-mode .pet-card {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.pet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.pet-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.dark-mode .pet-title {
  color: #ffffff;
}

.chevron-right {
  font-size: 32rpx;
  color: #9ca3af;
}

.pet-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.pet-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #fb923c, #fbbf24);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}

.pet-details {
  flex: 1;
}

.pet-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.dark-mode .pet-name {
  color: #ffffff;
}

.pet-mood {
  font-size: 24rpx;
  color: #666;
}

.dark-mode .pet-mood {
  color: #9ca3af;
}

.chat-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.15);
  border: 2rpx solid rgba(59, 130, 246, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
}

.chat-icon:active {
  transform: scale(0.95);
  background: rgba(59, 130, 246, 0.25);
}

.dark-mode .chat-icon {
  background: rgba(59, 130, 246, 0.25);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
}

.chat-emoji {
  font-size: 32rpx;
  color: #3b82f6;
}

.dark-mode .chat-emoji {
  color: #60a5fa;
}

/* Settings Section */
.settings-section {
  padding: 10rpx 40rpx 20rpx; /* 调整间距与首页保持一致 */
  display: flex;
  flex-direction: column;
  gap: 20rpx; /* 减少间距与首页保持一致 */
}

.setting-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 40rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.dark-mode .setting-card {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.setting-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.dark-mode .setting-title {
  color: #ffffff;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.setting-item:last-child {
  border-bottom: none;
}

.dark-mode .setting-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.setting-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.setting-icon {
  font-size: 32rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
}

.dark-mode .setting-label {
  color: #ffffff;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.setting-value {
  font-size: 24rpx;
  color: #666;
}

.dark-mode .setting-value {
  color: #9ca3af;
}

/* Toggle Switch */
.toggle-switch {
  width: 80rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #e5e7eb;
  position: relative;
  transition: all 0.3s ease;
}

.toggle-switch.active {
  background: #10b981;
}

.toggle-thumb {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ffffff;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.toggle-switch.active .toggle-thumb {
  transform: translateX(40rpx);
}

/* Logout Section */
.logout-section {
  padding: 10rpx 40rpx 20rpx; /* 调整间距与首页保持一致 */
}

.logout-button {
  width: 100%;
  padding: 25rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  transition: all 0.3s ease;
  box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.dark-mode .logout-button {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.logout-button:active {
  transform: scale(0.98);
  background: rgba(239, 68, 68, 0.1);
}

.dark-mode .logout-button:active {
  background: rgba(185, 28, 28, 0.3);
}

.logout-icon {
  font-size: 28rpx;
}

.logout-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #dc2626;
}

.dark-mode .logout-text {
  color: #f87171;
}

/* 登录提示样式 */
.login-prompt {
  display: flex;
  align-items: center;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
}

.dark-mode .login-avatar {
  background: linear-gradient(135deg, #374151, #4b5563);
}

.login-icon {
  font-size: 48rpx;
  color: #9ca3af;
}

.dark-mode .login-icon {
  color: #6b7280;
}

.login-info {
  flex: 1;
}

.login-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.dark-mode .login-title {
  color: #ffffff;
}

.login-desc {
  font-size: 24rpx;
  color: #666;
}

.dark-mode .login-desc {
  color: #d1d5db;
}

.login-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.login-buttons button {
  margin-bottom: 15rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
}

.login-buttons button::after {
  border: none;
}