<!DOCTYPE html><html><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;"><meta http-equiv="x-dns-prefetch-control" content="off"><meta name="x-poe-datastore-behavior" content="disabled"><meta name="x-poe-allow-downloads" content="true"><script src="https://puc.poecdn.net/standard.3ef2c256959faf5a756d.js"></script>
    <meta charset="utf-8">
    <title>Poe</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
  <script src="https://puc.poecdn.net/disableWebRTC.9710cebe07429a9e8e06.js"></script><script src="https://puc.poecdn.net/tw.b9024aecac666455e183.js"></script><script src="https://puc.poecdn.net/deps.faccd16000d314dc16d5.js"></script><script src="https://puc.poecdn.net/exports.b0f0f482cdeb5302b0b9.js"></script><script src="https://puc.poecdn.net/renderer.75c73ae6b4235f62945a.js"></script><script>Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }var _react = require('react'); var _react2 = _interopRequireDefault(_react);
var _lucidereact = require('lucide-react');

const PetApp = () => {
  const [petAnimation, setPetAnimation] = _react.useState.call(void 0, 'idle');
  const [showFloatingExp, setShowFloatingExp] = _react.useState.call(void 0, false);
  
  const petStats = {
    health: 96,
    energy: 80,
    intimacy: 100,
    experience: 150,
    maxExp: 800,
    level: 7,
    daysWithPet: 18,
    totalExp: 775
  };

  const tasks = [
    { name: '每日步行', reward: 20, completed: true },
    { name: '喝水打卡', reward: 15, completed: true },
    { name: '每日步行', reward: 20, completed: true },
    { name: '喝水打卡', reward: 15, completed: true },
    { name: '早睡早起', reward: 25, completed: true }
  ];

  const completedTasks = tasks.filter(task => task.completed).length;

  const handlePetInteraction = (action) => {
    setPetAnimation(action);
    setShowFloatingExp(true);
    setTimeout(() => {
      setPetAnimation('idle');
      setShowFloatingExp(false);
    }, 2000);
  };

  return (
    _react2.default.createElement('div', { className: "min-h-screen bg-gradient-to-b from-green-300 via-green-200 to-yellow-200"    ,}
      /* Status Bar */
      , _react2.default.createElement('div', { className: "flex justify-between items-center px-4 py-2 text-white text-sm"      ,}
        , _react2.default.createElement('div', { className: "flex items-center space-x-1"  ,}
          , _react2.default.createElement('div', { className: "flex space-x-1" ,}
            , [...Array(5)].map((_, i) => (
              _react2.default.createElement('div', { key: i, className: "w-1 h-1 bg-white rounded-full"   ,})
            ))
          )
          , _react2.default.createElement('span', { className: "ml-2",}, "WeChat")
        )
        , _react2.default.createElement('div', { className: "flex items-center space-x-2"  ,}
          , _react2.default.createElement('span', null, "100%")
          , _react2.default.createElement('div', { className: "w-6 h-3 border border-white rounded-sm"    ,}
            , _react2.default.createElement('div', { className: "w-full h-full bg-white rounded-sm"   ,})
          )
        )
      )

      /* Header */
      , _react2.default.createElement('div', { className: "flex justify-between items-center px-6 py-4"    ,}
        , _react2.default.createElement('h1', { className: "text-lg font-medium text-gray-800"  ,}, "我的宠物")
        , _react2.default.createElement('div', { className: "flex space-x-2" ,}
          , _react2.default.createElement('button', { className: "w-8 h-8 bg-black bg-opacity-20 rounded-full flex items-center justify-center"       ,}
            , _react2.default.createElement(_lucidereact.MoreHorizontal, { className: "w-4 h-4 text-white"  ,} )
          )
          , _react2.default.createElement('button', { className: "w-8 h-8 bg-black bg-opacity-20 rounded-full flex items-center justify-center"       ,}
            , _react2.default.createElement(_lucidereact.Settings, { className: "w-4 h-4 text-white"  ,} )
          )
        )
      )

      /* Pet Display Area */
      , _react2.default.createElement('div', { className: "mx-6 mb-6" ,}
        , _react2.default.createElement('div', { className: "bg-gradient-to-br from-cyan-200 to-cyan-300 rounded-3xl p-6 relative overflow-hidden"      ,}
          /* Background pattern */
          , _react2.default.createElement('div', { className: "absolute inset-0 opacity-30"  ,}
            , _react2.default.createElement('div', { className: "w-full h-full bg-repeat"  , style: {
              backgroundImage: `repeating-linear-gradient(90deg, transparent, transparent 10px, rgba(255,255,255,0.1) 10px, rgba(255,255,255,0.1) 11px)`
            },})
          )

          /* Clouds */
          , _react2.default.createElement('div', { className: "absolute top-4 left-8 w-12 h-6 bg-white rounded-full opacity-80"       ,})
          , _react2.default.createElement('div', { className: "absolute top-4 right-12 w-8 h-4 bg-white rounded-full opacity-60"       ,})

          /* Level Badge */
          , _react2.default.createElement('div', { className: "absolute top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold"         ,}, "Lv."
            , petStats.level
          )

          /* Pet Character */
          , _react2.default.createElement('div', { className: "flex justify-center items-center h-40 relative"    ,}
            , _react2.default.createElement('div', { className: `text-6xl transition-transform duration-500 ${
              petAnimation === 'feed' ? 'scale-110' : 
              petAnimation === 'play' ? 'animate-bounce' :
              petAnimation === 'walk' ? 'translate-x-2' : ''
            }`,}, "🦊"

            )

            /* Floating Experience */
            , showFloatingExp && (
              _react2.default.createElement('div', { className: "absolute -top-4 left-1/2 transform -translate-x-1/2 text-yellow-500 font-bold text-lg animate-bounce"        ,}, "+25 EXP!"

              )
            )
          )

          /* Pet Name and Status */
          , _react2.default.createElement('div', { className: "text-center mt-4" ,}
            , _react2.default.createElement('h2', { className: "text-xl font-bold text-gray-800 mb-1"   ,}, "小绿")
            , _react2.default.createElement('p', { className: "text-gray-700",}, "非常开心")
          )
        )
      )

      /* Stats Panel */
      , _react2.default.createElement('div', { className: "mx-6 mb-6" ,}
        , _react2.default.createElement('div', { className: "bg-white rounded-2xl p-4 shadow-sm"   ,}
          , _react2.default.createElement('div', { className: "space-y-3",}
            , _react2.default.createElement('div', { className: "flex justify-between items-center"  ,}
              , _react2.default.createElement('span', { className: "text-gray-800 font-medium" ,}, "健康值")
              , _react2.default.createElement('span', { className: "text-gray-600 text-sm" ,}, petStats.health, "/100")
            )
            , _react2.default.createElement('div', { className: "w-full bg-gray-200 rounded-full h-2"   ,}
              , _react2.default.createElement('div', { className: "bg-green-500 h-2 rounded-full transition-all duration-1000"    , 
                   style: { width: `${petStats.health}%` },})
            )

            , _react2.default.createElement('div', { className: "flex justify-between items-center"  ,}
              , _react2.default.createElement('span', { className: "text-gray-800 font-medium" ,}, "活力值")
              , _react2.default.createElement('span', { className: "text-gray-600 text-sm" ,}, petStats.energy, "/100")
            )
            , _react2.default.createElement('div', { className: "w-full bg-gray-200 rounded-full h-2"   ,}
              , _react2.default.createElement('div', { className: "bg-yellow-500 h-2 rounded-full transition-all duration-1000"    , 
                   style: { width: `${petStats.energy}%` },})
            )

            , _react2.default.createElement('div', { className: "flex justify-between items-center"  ,}
              , _react2.default.createElement('span', { className: "text-gray-800 font-medium" ,}, "亲密度")
              , _react2.default.createElement('span', { className: "text-gray-600 text-sm" ,}, petStats.intimacy, "/100")
            )
            , _react2.default.createElement('div', { className: "w-full bg-gray-200 rounded-full h-2"   ,}
              , _react2.default.createElement('div', { className: "bg-orange-500 h-2 rounded-full transition-all duration-1000"    , 
                   style: { width: `${petStats.intimacy}%` },})
            )
          )
        )
      )

      /* Experience Panel */
      , _react2.default.createElement('div', { className: "mx-6 mb-6" ,}
        , _react2.default.createElement('div', { className: "bg-white rounded-2xl p-4 shadow-sm"   ,}
          , _react2.default.createElement('div', { className: "flex justify-between items-center mb-2"   ,}
            , _react2.default.createElement('span', { className: "text-gray-800 font-medium" ,}, "经验值")
            , _react2.default.createElement('span', { className: "text-gray-600 text-sm" ,}, petStats.experience, "/", petStats.maxExp)
          )
          , _react2.default.createElement('div', { className: "w-full bg-gray-200 rounded-full h-3"   ,}
            , _react2.default.createElement('div', { className: "bg-gradient-to-r from-orange-400 to-yellow-500 h-3 rounded-full transition-all duration-1000"      , 
                 style: { width: `${(petStats.experience / petStats.maxExp) * 100}%` },})
          )
        )
      )

      /* Stats Summary */
      , _react2.default.createElement('div', { className: "mx-6 mb-6" ,}
        , _react2.default.createElement('div', { className: "flex justify-between text-center"  ,}
          , _react2.default.createElement('div', null
            , _react2.default.createElement('div', { className: "text-2xl font-bold text-orange-500"  ,}, petStats.daysWithPet)
            , _react2.default.createElement('div', { className: "text-sm text-gray-600" ,}, "陪伴天数")
          )
          , _react2.default.createElement('div', null
            , _react2.default.createElement('div', { className: "text-2xl font-bold text-orange-500"  ,}, petStats.totalExp)
            , _react2.default.createElement('div', { className: "text-sm text-gray-600" ,}, "总经验")
          )
        )
      )

      /* Action Buttons */
      , _react2.default.createElement('div', { className: "mx-6 mb-6" ,}
        , _react2.default.createElement('div', { className: "grid grid-cols-4 gap-4"  ,}
          , [
            { icon: _lucidereact.Utensils, label: '喂食', color: 'bg-red-100 text-red-600', action: 'feed' },
            { icon: _lucidereact.HandHeart, label: '互动', color: 'bg-green-100 text-green-600', action: 'play' },
            { icon: _lucidereact.Footprints, label: '散步', color: 'bg-blue-100 text-blue-600', action: 'walk' },
            { icon: _lucidereact.GamepadIcon, label: '游戏', color: 'bg-purple-100 text-purple-600', action: 'play' }
          ].map((action, index) => {
            const Icon = action.icon;
            return (
              _react2.default.createElement('button', {
                key: index,
                onClick: () => handlePetInteraction(action.action),
                className: `${action.color} rounded-2xl p-4 flex flex-col items-center justify-center space-y-2 transition-transform hover:scale-105 active:scale-95`,}

                , _react2.default.createElement(Icon, { className: "w-6 h-6" ,} )
                , _react2.default.createElement('span', { className: "text-sm font-medium" ,}, action.label)
              )
            );
          })
        )
      )

      /* Tasks Panel */
      , _react2.default.createElement('div', { className: "mx-6 mb-6" ,}
        , _react2.default.createElement('div', { className: "bg-white rounded-2xl p-4 shadow-sm"   ,}
          , _react2.default.createElement('div', { className: "flex justify-between items-center mb-4"   ,}
            , _react2.default.createElement('h3', { className: "text-lg font-bold text-gray-800"  ,}, "今日任务")
            , _react2.default.createElement('span', { className: "text-orange-500 font-semibold" ,}, completedTasks, "/", tasks.length)
          )

          , _react2.default.createElement('div', { className: "space-y-3",}
            , tasks.map((task, index) => (
              _react2.default.createElement('div', { key: index, className: "flex justify-between items-center"  ,}
                , _react2.default.createElement('div', { className: "flex items-center space-x-3"  ,}
                  , _react2.default.createElement('div', { className: `w-4 h-4 rounded-full ${task.completed ? 'bg-green-500' : 'bg-gray-300'}`,}
                    , task.completed && (
                      _react2.default.createElement('div', { className: "w-full h-full flex items-center justify-center"    ,}
                        , _react2.default.createElement('span', { className: "text-white text-xs" ,}, "✓")
                      )
                    )
                  )
                  , _react2.default.createElement('span', { className: "text-gray-800",}, task.name)
                )
                , _react2.default.createElement('span', { className: "text-orange-500 text-sm font-medium"  ,}, "+", task.reward, "经验")
              )
            ))
          )
        )
      )

      /* Bottom Navigation */
      , _react2.default.createElement('div', { className: "fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200"      ,}
        , _react2.default.createElement('div', { className: "grid grid-cols-5 py-2"  ,}
          , [
            { icon: '🏠', label: '宠物主页', active: true },
            { icon: '📋', label: '健康任务' },
            { icon: '💬', label: '宠物聊天' },
            { icon: '📊', label: '健康数据' },
            { icon: '⚙️', label: '个人设置' }
          ].map((nav, index) => (
            _react2.default.createElement('button', {
              key: index,
              className: `flex flex-col items-center py-2 ${
                nav.active ? 'text-green-600' : 'text-gray-500'
              }`,}

              , _react2.default.createElement('span', { className: "text-lg mb-1" ,}, nav.icon)
              , _react2.default.createElement('span', { className: "text-xs",}, nav.label)
            )
          ))
        )
      )
    )
  );
};

exports. default = PetApp;
</script></head>
  <body>
  

<div id="preview-app"></div></body></html>