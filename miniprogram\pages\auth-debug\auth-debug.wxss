/* pages/auth-debug/auth-debug.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.section {
  background-color: white;
  margin-bottom: 20rpx;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.test-btn {
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  font-size: 30rpx;
}

.test-btn:last-child {
  margin-bottom: 0;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 10rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.gender, .location {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  font-size: 28rpx;
  color: #666;
}

.result-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.log-actions {
  display: flex;
  gap: 10rpx;
}

.log-container {
  height: 400rpx;
  background-color: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-item {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
  padding: 10rpx;
  background-color: white;
  border-radius: 5rpx;
  border-left: 3rpx solid #667eea;
}

.log-empty {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 50rpx 0;
}

.tips {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border: none;
}

.tips .section-title {
  color: #d63031;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  margin-top: 5rpx;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  color: #2d3436;
  line-height: 1.5;
}

.tip-item.important {
  background-color: rgba(255, 255, 255, 0.3);
  padding: 15rpx;
  border-radius: 10rpx;
  border-left: 5rpx solid #e74c3c;
}

.tip-item.important .tip-text {
  font-weight: bold;
  color: #c0392b;
}
