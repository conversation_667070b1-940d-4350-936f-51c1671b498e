// app.js
App({
  onLaunch: function () {
    // 初始化全局数据
    this.globalData = {
      userInfo: null,
      petData: null,
      env: "",
      isLoggedIn: false
    };

    // 初始化云开发
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: 'cloud1-8gino5ste2508c07',
        traceUser: true,
      });
    }

    // 检查登录状态
    this.checkLoginStatus();
    
    // 初始化宠物数据
    this.initPetData();
  },

  onShow: function () {
    // 小程序显示时更新数据
    this.updatePetStatus();
  },

  // 初始化宠物数据
  initPetData: function () {
    try {
      let petData = wx.getStorageSync('petData');
      if (!petData) {
        // 创建默认宠物数据
        petData = {
          id: 'pet_' + Date.now(),
          name: '小绿',
          level: 1,
          experience: 0,
          maxExperience: 100,
          attributes: {
            health: 100,
            energy: 100,
            intimacy: 50
          },
          status: '开心中',
          lastUpdateTime: new Date(),
          totalDays: 1,
          totalExperience: 0
        };
        wx.setStorageSync('petData', petData);
      }
      this.globalData.petData = petData;
    } catch (error) {
      console.error('初始化宠物数据失败:', error);
    }
  },

  // 更新宠物状态
  updatePetStatus: function () {
    try {
      const petData = wx.getStorageSync('petData');
      if (petData) {
        const now = new Date();
        const lastUpdate = new Date(petData.lastUpdateTime);
        const timeDiff = now - lastUpdate;
        
        // 如果超过一天，更新陪伴天数
        if (timeDiff > 24 * 60 * 60 * 1000) {
          petData.totalDays += Math.floor(timeDiff / (24 * 60 * 60 * 1000));
          petData.lastUpdateTime = now;
          wx.setStorageSync('petData', petData);
          this.globalData.petData = petData;
        }
      }
    } catch (error) {
      console.error('更新宠物状态失败:', error);
    }
  },

  // 获取宠物数据
  getPetData: function () {
    return this.globalData.petData || wx.getStorageSync('petData');
  },

  // 保存宠物数据
  savePetData: function (petData) {
    try {
      wx.setStorageSync('petData', petData);
      this.globalData.petData = petData;
    } catch (error) {
      console.error('保存宠物数据失败:', error);
    }
  },

  // 检查登录状态
  checkLoginStatus: function () {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.globalData.userInfo = userInfo;
        this.globalData.isLoggedIn = true;
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }
  },

  // 微信登录 - 完整流程：wx.getUserProfile + wx.login + 云函数获取openid + 数据库存储
  wxLogin: function () {
    return new Promise(async (resolve, reject) => {
      try {
        // 第一步：获取用户头像和昵称
        const profileRes = await wx.getUserProfile({
          desc: '用于完善会员资料'
        });
        
        const userInfo = profileRes.userInfo;
        console.log('获取用户信息成功:', userInfo);
        
        // 第二步：登录获取code
        const loginRes = await wx.login();
        console.log('登录成功，获取到 code:', loginRes.code);
        
        // 检查云开发是否可用
        if (!wx.cloud) {
          throw new Error('云开发不可用，请检查基础库版本');
        }
        
        try {
          // 第三步：通过云函数获取openid
          console.log('开始调用云函数 getOpenid...');
          const cloudRes = await wx.cloud.callFunction({
            name: 'getOpenid',
            env: 'cloud1-8gino5ste2508c07',
            data: { code: loginRes.code }
          });
          
          console.log('云函数调用结果:', cloudRes);
          const openid = cloudRes.result.openid;
          console.log('获取openid成功:', openid);
          
          // 第四步：保存用户信息到数据库
          const db = wx.cloud.database({
            env: 'cloud1-8gino5ste2508c07'
          });
          const users = db.collection('users');
          
          // 检查用户是否已存在
          const existing = await users.where({ _openid: openid }).get();
          const now = db.serverDate();
          
          if (existing.data.length === 0) {
            // 新用户，添加到数据库
            await users.add({
              data: {
                nickName: userInfo.nickName,
                avatarUrl: userInfo.avatarUrl,
                gender: userInfo.gender,
                province: userInfo.province,
                city: userInfo.city,
                country: userInfo.country,
                language: userInfo.language,
                createTime: now,
                lastLoginTime: now,
                role: 'user',
                phone: ''
              }
            });
            console.log('新用户信息已保存到数据库');
          } else {
            // 已存在用户，更新最后登录时间和用户信息
            await users.where({ _openid: openid }).update({
              data: {
                nickName: userInfo.nickName,
                avatarUrl: userInfo.avatarUrl,
                lastLoginTime: now
              }
            });
            console.log('用户信息已更新');
          }
          
          // 第五步：保存用户信息到本地
          const localUserInfo = {
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            gender: userInfo.gender,
            country: userInfo.country,
            province: userInfo.province,
            city: userInfo.city,
            language: userInfo.language,
            openid: openid,
            loginTime: new Date().toISOString()
          };
          
          wx.setStorageSync('userInfo', localUserInfo);
          this.globalData.userInfo = localUserInfo;
          this.globalData.isLoggedIn = true;
          
          resolve(localUserInfo);
          
        } catch (cloudError) {
          console.warn('云开发功能不可用，使用本地模式:', cloudError);
          
          // 云开发不可用时的降级方案
          const localUserInfo = {
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            gender: userInfo.gender,
            country: userInfo.country,
            province: userInfo.province,
            city: userInfo.city,
            language: userInfo.language,
            openid: 'local_' + Date.now(), // 本地生成的临时ID
            loginTime: new Date().toISOString(),
            isLocalMode: true // 标记为本地模式
          };
          
          wx.setStorageSync('userInfo', localUserInfo);
          this.globalData.userInfo = localUserInfo;
          this.globalData.isLoggedIn = true;
          
          wx.showToast({
            title: '登录成功（本地模式）',
            icon: 'success'
          });
          
          resolve(localUserInfo);
        }
        
      } catch (error) {
        console.error('登录过程出错:', error);
        reject(error);
      }
    });
  },

  // 退出登录
  logout: function () {
    try {
      wx.removeStorageSync('userInfo');
      this.globalData.userInfo = null;
      this.globalData.isLoggedIn = false;
      return true;
    } catch (error) {
      console.error('退出登录失败:', error);
      return false;
    }
  },

  // 获取用户信息（获取已保存的）
  getUserInfo: function () {
    return this.globalData.userInfo || wx.getStorageSync('userInfo');
  },

  // 检查是否已登录
  isLoggedIn: function () {
    return this.globalData.isLoggedIn;
  }
});
