<!--components/task-list/task-list.wxml-->
<view class="task-list-container">
  <!-- 任务进度头部 -->
  <view class="task-header" wx:if="{{showProgress}}">
    <view class="progress-info">
      <text class="progress-title">今日任务</text>
      <text class="progress-count">{{completedCount}}/{{totalCount}}</text>
    </view>
    
    <!-- 总进度条 -->
    <view class="overall-progress">
      <view class="progress-bg"></view>
      <view 
        class="progress-fill" 
        style="width: {{progressPercentage}}%"
      ></view>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="task-items">
    <view 
      class="task-item {{item.completed ? 'completed' : ''}} {{item.animationClass || ''}}"
      wx:for="{{displayTasks}}" 
      wx:key="id"
      data-index="{{index}}"
      bindtap="onTaskTap"
    >
      <!-- 任务状态指示器 -->
      <view class="task-status">
        <view class="status-dot {{item.completed ? 'completed' : 'pending'}}">
          <image 
            wx:if="{{item.completed}}" 
            src="/images/icons/check.png" 
            class="check-icon"
          ></image>
        </view>
      </view>

      <!-- 任务内容 -->
      <view class="task-content">
        <!-- 任务图标和标题 -->
        <view class="task-main">
          <image 
            src="{{getTaskIcon(item.type)}}" 
            class="task-icon"
          ></image>
          <view class="task-info">
            <text class="task-name">{{item.name}}</text>
            <text class="task-desc">{{formatTaskDescription(item)}}</text>
          </view>
        </view>

        <!-- 任务进度条 (如果有进度) -->
        <view class="task-progress" wx:if="{{item.progress !== undefined && item.target}}">
          <view class="mini-progress-bg"></view>
          <view 
            class="mini-progress-fill" 
            style="width: {{(item.progress / item.target) * 100}}%"
          ></view>
        </view>
      </view>

      <!-- 奖励信息 -->
      <view class="task-reward">
        <text class="reward-text">+{{item.reward}}</text>
        <text class="reward-unit">经验</text>
      </view>

      <!-- 完成特效 -->
      <view class="complete-effect" wx:if="{{item.completed}}">
        <view class="effect-circle"></view>
      </view>
    </view>
  </view>

  <!-- 奖励动画 -->
  <view class="reward-animation {{showRewardAnimation ? 'show' : ''}}" wx:if="{{showRewardAnimation}}">
    <text class="reward-animation-text">{{rewardText}}</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{displayTasks.length === 0}}">
    <image src="/images/icons/empty-task.png" class="empty-icon"></image>
    <text class="empty-text">暂无任务</text>
  </view>
</view>