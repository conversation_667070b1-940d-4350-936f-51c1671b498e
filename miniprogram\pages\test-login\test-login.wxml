<!--pages/test-login/test-login.wxml-->
<view class="container">
  <view class="title">登录功能测试</view>
  
  <!-- 用户信息显示 -->
  <view class="user-info" wx:if="{{hasUserInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}" wx:if="{{userInfo.avatarUrl}}"></image>
    <view class="nickname">{{userInfo.nickName}}</view>
    <view class="details">
      <text>性别: {{userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '未知'}}</text>
      <text>地区: {{userInfo.country}} {{userInfo.province}} {{userInfo.city}}</text>
    </view>
  </view>
  
  <!-- 测试按钮 -->
  <view class="buttons">
    <button type="primary" bindtap="testGetUserProfile">测试 getUserProfile</button>
    <button type="default" bindtap="testFullLogin">测试完整登录流程</button>
  </view>
  
  <!-- 说明文字 -->
  <view class="tips">
    <text class="tip-title">测试说明：</text>
    <text class="tip-text">1. 点击"测试 getUserProfile"验证基础API</text>
    <text class="tip-text">2. 点击"测试完整登录流程"验证整个登录过程</text>
    <text class="tip-text">3. 查看控制台日志获取详细信息</text>
  </view>
</view>