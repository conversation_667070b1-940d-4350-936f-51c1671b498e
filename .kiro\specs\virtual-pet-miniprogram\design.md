# 设计文档

## 概述

虚拟宠物养成小程序是一个基于微信小程序框架开发的轻量级养成游戏。采用MVVM架构模式，使用微信小程序原生开发框架，结合本地存储和云开发能力，为用户提供流畅的虚拟宠物养成体验。

## 架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   视图层 (View)   │    │  逻辑层 (Logic)  │    │  数据层 (Data)   │
│                 │    │                 │    │                 │
│ - WXML 模板     │◄──►│ - JavaScript    │◄──►│ - 本地存储       │
│ - WXSS 样式     │    │ - 业务逻辑       │    │ - 云数据库       │
│ - 组件系统       │    │ - 数据处理       │    │ - 静态资源       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈
- **前端框架**: 微信小程序原生框架
- **开发语言**: JavaScript ES6+
- **样式语言**: WXSS (WeiXin Style Sheets)
- **模板语言**: WXML (WeiXin Markup Language)
- **数据存储**: wx.setStorageSync / wx.getStorageSync
- **云服务**: 微信云开发 (可选)

## 组件和接口

### 页面结构
```
pages/
├── index/                 # 宠物主页
│   ├── index.wxml        # 宠物展示、属性条、功能按钮
│   ├── index.wxss        # 绿色主题样式、卡片布局
│   ├── index.js          # 宠物数据管理、属性更新逻辑
│   └── index.json        # 页面配置
├── health/               # 健康任务页
│   ├── index.wxml        # 日常任务列表、完成状态
│   ├── index.wxss        # 任务卡片样式、进度显示
│   ├── index.js          # 任务管理逻辑、奖励发放
│   └── index.json
├── chat/                 # 宠物聊天页
├── data/                 # 健康数据页
└── profile/              # 个人设置页
```

### 界面设计要点

#### 主页面设计 (基于提供的截图)
1. **宠物展示区域**
   - 像素风格宠物形象 (橙色小龙造型)
   - 等级显示 (Lv.7 红色标签)
   - 宠物名称和状态文字
   - 绿色草地背景，营造自然环境

2. **属性显示区域**
   - 健康值：绿色进度条 (76/100)
   - 活力值：黄色进度条 (52/100) 
   - 亲密度：橙色进度条 (100/100)
   - 经验值：渐变色进度条 (150/800)

3. **统计信息**
   - 陪伴天数：18天
   - 总经验：775

4. **功能按钮区域**
   - 四个圆形按钮：喂食、互动、散步、游戏
   - 每个按钮有对应的彩色图标

#### 任务页面设计 (基于第二张截图)
1. **任务列表区域**
   - 今日任务标题和完成进度 (16/16)
   - 任务项目：每日步行、喝水打卡、早睡早起
   - 每个任务显示奖励经验值
   - 完成状态用绿色圆点标识

2. **布局特点**
   - 保持与主页相同的绿色主题
   - 白色卡片式设计
   - 清晰的层次结构

#### 聊天页面设计 (基于聊天界面截图)
1. **页面头部**
   - 黄绿色渐变背景，与主题保持一致
   - 页面标题"宠物聊天"居中显示
   - 右上角设置按钮和帮助按钮

2. **宠物信息区域**
   - 宠物头像（绿色圆形头像）
   - 宠物名称"小绿"
   - 当前状态描述"开心聊天中"
   - 右侧可能有宠物心情或状态图标

3. **聊天对话区域**
   - 白色背景的对话框区域
   - 宠物形象居中显示（简化的绿色宠物图标）
   - 支持显示宠物的回复消息
   - 消息气泡样式设计

4. **输入交互区域**
   - 底部输入框，显示预设消息如"下午好"
   - 橙色发送按钮，圆形设计
   - 支持快捷回复和自定义输入

5. **设计特点**
   - 整体采用温暖的黄绿色调
   - 简洁的对话界面设计
   - 突出宠物的可爱形象
   - 友好的交互体验

#### 健康数据页面设计 (基于健康数据截图)
1. **数据概览区域**
   - 今日数据卡片：显示步数(112步)、饮水(1810ml)、睡眠(8h)、运动(60min)
   - 每项数据配有彩色图标和趋势指示
   - 日期显示和数据更新状态

2. **数据分类导航**
   - 四个主要分类：步数、饮水、睡眠、运动
   - 选中状态用橙色高亮显示
   - 支持切换不同数据类型的详细视图

3. **时间范围选择**
   - 今日、本周、本月三个时间维度
   - 绿色按钮表示当前选中的时间范围
   - 支持不同时间段的数据对比

4. **详细数据展示**
   - **步数统计**：圆形进度图显示完成度(1%)，目标8000步
   - **运动统计**：显示运动时长(60分钟)、消耗卡路里(320)、平均心率(0)
   - **运动时间分布**：柱状图显示一周内每天的运动时间分布
   - **饮水记录**：水杯可视化显示当前饮水量(2310ml)，目标2000ml
   - **睡眠分析**：显示入睡时间(23:30)、起床时间(07:00)、睡眠时长(8小时)
   - **睡眠质量**：圆形图表显示睡眠质量评分(85分)
   - **睡眠阶段**：详细显示深度睡眠、浅度睡眠、REM睡眠的时长分布

5. **交互功能**
   - **快速添加**：饮水记录支持200ml、300ml、500ml快速添加按钮
   - **数据同步**：显示"正在同步数据..."的加载状态
   - **数据导出**：支持导出数据功能
   - **数据设置**：可配置目标值和提醒设置

6. **激励元素**
   - 数据提醒："今日还需7888步即可达标，加油！"
   - 完成度可视化：进度条和百分比显示
   - 趋势图标：上升/下降箭头显示数据变化趋势

#### 个人设置页面设计 (基于个人设置截图)
1. **用户信息区域**
   - 用户头像（圆形头像，支持自定义）
   - 微信用户昵称显示
   - 用户等级标识（Lv.）
   - 陪伴天数、完成任务、总经验等统计信息

2. **我的宠物区域**
   - 宠物头像和名称（小绿）
   - 宠物等级（Lv.7）和称号（健康小助手）
   - 宠物当前心情状态（非常开心）
   - 支持点击查看宠物详情

3. **通知设置区域**
   - 任务提醒：开关控制，支持开启/关闭
   - 饮水提醒：开关控制，绿色表示已开启
   - 睡眠提醒：开关控制，灰色表示已关闭
   - 每个提醒都有对应的彩色图标

4. **隐私设置区域**
   - 数据同步：开关控制数据云端同步
   - 隐私政策：点击查看详细隐私条款
   - 右箭头表示可点击进入详情页

5. **应用设置区域**
   - 主题设置：显示当前主题（自动），支持切换
   - 语言设置：显示当前语言（简体中文），支持多语言
   - 清理缓存：显示当前缓存大小（16.0MB），支持一键清理

6. **关于区域**
   - 检查更新：显示当前版本（v1.0.0），支持版本检查
   - 关于我们：查看应用介绍和开发团队信息
   - 意见反馈：用户反馈入口

7. **登录页面设计**
   - 渐变背景（绿色到黄色）
   - 应用Logo和名称"健康宠物伴侣"
   - 简洁的笑脸图标设计
   - 微信登录按钮（绿色圆角按钮）
   - 返回首页按钮

8. **退出登录功能**
   - 橙色退出登录按钮
   - 确认对话框防止误操作
   - 退出后返回登录页面

9. **设计特点**
   - 分组卡片式布局，层次清晰
   - 开关控件统一使用绿色主题色
   - 图标和文字搭配，提高可读性
   - 渐变背景营造温馨氛围

### 核心组件设计

#### 1. 宠物展示组件 (PetDisplay)
```javascript
// components/pet-display/pet-display.js
Component({
  properties: {
    petData: {
      type: Object,
      value: {}
    }
  },
  data: {
    animationClass: '',
    statusText: ''
  },
  methods: {
    updatePetAnimation() {
      // 根据宠物状态更新动画
    },
    onPetTap() {
      // 宠物点击交互
    }
  }
})
```

#### 2. 属性条组件 (AttributeBar)
```javascript
// components/attribute-bar/attribute-bar.js
Component({
  properties: {
    label: String,
    current: Number,
    max: Number,
    color: String
  },
  data: {
    percentage: 0
  },
  observers: {
    'current, max': function(current, max) {
      this.setData({
        percentage: (current / max) * 100
      })
    }
  }
})
```

#### 3. 任务列表组件 (TaskList)
```javascript
// components/task-list/task-list.js
Component({
  properties: {
    tasks: {
      type: Array,
      value: []
    }
  },
  methods: {
    onTaskComplete(e) {
      const taskId = e.currentTarget.dataset.id
      this.triggerEvent('taskComplete', { taskId })
    }
  }
})
```

#### 4. 聊天组件 (ChatBox)
```javascript
// components/chat-box/chat-box.js
Component({
  properties: {
    petData: {
      type: Object,
      value: {}
    }
  },
  data: {
    messages: [],
    inputText: '',
    quickReplies: ['下午好', '你好吗', '今天开心吗', '想吃什么']
  },
  methods: {
    // 发送消息
    sendMessage(e) {
      const message = e.detail.value || this.data.inputText
      if (!message.trim()) return
      
      this.addMessage(message, 'user')
      this.generatePetReply(message)
      this.setData({ inputText: '' })
    },
    
    // 添加消息到聊天记录
    addMessage(content, sender) {
      const message = {
        id: Date.now(),
        content,
        sender, // 'user' or 'pet'
        timestamp: new Date()
      }
      
      this.setData({
        messages: [...this.data.messages, message]
      })
    },
    
    // 生成宠物回复
    generatePetReply(userMessage) {
      setTimeout(() => {
        const reply = this.getPetReply(userMessage)
        this.addMessage(reply, 'pet')
        
        // 增加亲密度
        this.triggerEvent('updateIntimacy', { value: 1 })
      }, 1000)
    },
    
    // 获取宠物回复内容
    getPetReply(message) {
      const replies = {
        '下午好': '下午好！今天天气真不错呢~',
        '你好吗': '我很好呀！谢谢你关心我~',
        '今天开心吗': '和你聊天我就很开心！',
        '想吃什么': '我想吃美味的小鱼干！'
      }
      
      return replies[message] || '谢谢你和我聊天，我很开心！'
    },
    
    // 快捷回复
    onQuickReply(e) {
      const reply = e.currentTarget.dataset.reply
      this.setData({ inputText: reply })
    }
  }
})
```

#### 5. 健康数据组件 (HealthData)
```javascript
// components/health-data/health-data.js
Component({
  properties: {
    dataType: {
      type: String,
      value: 'steps' // 'steps', 'water', 'sleep', 'exercise'
    },
    timeRange: {
      type: String,
      value: 'today' // 'today', 'week', 'month'
    }
  },
  data: {
    healthData: {},
    chartData: [],
    loading: false
  },
  methods: {
    // 切换数据类型
    switchDataType(e) {
      const type = e.currentTarget.dataset.type
      this.setData({ dataType: type })
      this.loadHealthData()
    },
    
    // 切换时间范围
    switchTimeRange(e) {
      const range = e.currentTarget.dataset.range
      this.setData({ timeRange: range })
      this.loadHealthData()
    },
    
    // 加载健康数据
    loadHealthData() {
      this.setData({ loading: true })
      
      const { dataType, timeRange } = this.data
      const healthManager = new HealthDataManager()
      
      healthManager.getHealthData(dataType, timeRange).then(data => {
        this.setData({
          healthData: data,
          chartData: this.formatChartData(data),
          loading: false
        })
      })
    },
    
    // 快速添加数据（如饮水）
    quickAdd(e) {
      const amount = e.currentTarget.dataset.amount
      const healthManager = new HealthDataManager()
      
      healthManager.addWaterRecord(amount).then(() => {
        this.loadHealthData()
        wx.showToast({
          title: `已添加${amount}ml`,
          icon: 'success'
        })
      })
    }
  }
})
```

#### 6. 个人设置组件 (ProfileSettings)
```javascript
// components/profile-settings/profile-settings.js
Component({
  properties: {
    userInfo: {
      type: Object,
      value: {}
    }
  },
  data: {
    settings: {
      taskReminder: true,
      waterReminder: true,
      sleepReminder: false,
      dataSync: true,
      theme: 'auto',
      language: 'zh-CN'
    },
    cacheSize: '16.0MB',
    version: 'v1.0.0'
  },
  methods: {
    // 切换设置开关
    toggleSetting(e) {
      const { setting } = e.currentTarget.dataset
      const currentValue = this.data.settings[setting]
      
      this.setData({
        [`settings.${setting}`]: !currentValue
      })
      
      this.saveSettings()
      this.triggerEvent('settingChange', {
        setting,
        value: !currentValue
      })
    },
    
    // 保存设置
    saveSettings() {
      wx.setStorageSync('userSettings', this.data.settings)
    },
    
    // 清理缓存
    clearCache() {
      wx.showModal({
        title: '清理缓存',
        content: '确定要清理缓存吗？',
        success: (res) => {
          if (res.confirm) {
            wx.clearStorageSync()
            this.setData({ cacheSize: '0MB' })
            wx.showToast({
              title: '缓存已清理',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 退出登录
    logout() {
      wx.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            wx.removeStorageSync('userInfo')
            wx.redirectTo({
              url: '/pages/login/login'
            })
          }
        }
      })
    }
  }
})
```

### 数据管理接口

#### 宠物数据管理器 (PetDataManager)
```javascript
// utils/petDataManager.js
class PetDataManager {
  constructor() {
    this.storageKey = 'petData'
  }
  
  // 获取宠物数据
  getPetData() {
    return wx.getStorageSync(this.storageKey) || this.getDefaultPetData()
  }
  
  // 保存宠物数据
  savePetData(data) {
    wx.setStorageSync(this.storageKey, data)
  }
  
  // 更新属性值
  updateAttribute(attribute, value) {
    const petData = this.getPetData()
    petData.attributes[attribute] = Math.max(0, Math.min(100, value))
    this.savePetData(petData)
    return petData
  }
  
  // 添加经验值
  addExperience(exp) {
    const petData = this.getPetData()
    petData.experience += exp
    
    // 检查升级
    if (petData.experience >= petData.maxExperience) {
      petData.level++
      petData.experience = 0
      petData.maxExperience = this.calculateMaxExp(petData.level)
    }
    
    this.savePetData(petData)
    return petData
  }
}
```

#### 任务管理器 (TaskManager)
```javascript
// utils/taskManager.js
class TaskManager {
  constructor() {
    this.storageKey = 'dailyTasks'
  }
  
  // 获取今日任务
  getTodayTasks() {
    const today = new Date().toDateString()
    const stored = wx.getStorageSync(this.storageKey)
    
    if (!stored || stored.date !== today) {
      return this.resetDailyTasks()
    }
    
    return stored.tasks
  }
  
  // 完成任务
  completeTask(taskId) {
    const taskData = wx.getStorageSync(this.storageKey)
    const task = taskData.tasks.find(t => t.id === taskId)
    
    if (task && !task.completed) {
      task.completed = true
      wx.setStorageSync(this.storageKey, taskData)
      
      // 奖励经验值
      const petManager = new PetDataManager()
      petManager.addExperience(task.reward)
      
      return task.reward
    }
    
    return 0
  }
}
```

#### 健康数据管理器 (HealthDataManager)
```javascript
// utils/healthDat

## 数据模型

### 宠物数据模型
```javascript
const PetDataSchema = {
  id: String,           // 宠物ID
  name: String,         // 宠物名称
  level: Number,        // 等级
  experience: Number,   // 当前经验值
  maxExperience: Number,// 升级所需经验值
  attributes: {
    health: Number,     // 健康值 (0-100)
    energy: Number,     // 活力值 (0-100)
    intimacy: Number    // 亲密度 (0-100)
  },
  status: String,       // 当前状态描述
  lastUpdateTime: Date, // 最后更新时间
  totalDays: Number,    // 陪伴天数
  totalExperience: Number // 总经验值
}
```

### 任务数据模型
```javascript
const TaskSchema = {
  id: String,           // 任务ID
  name: String,         // 任务名称
  description: String,  // 任务描述
  type: String,         // 任务类型 (daily, weekly, achievement)
  reward: Number,       // 奖励经验值
  completed: Boolean,   // 完成状态
  completedAt: Date     // 完成时间
}
```

### 聊天数据模型
```javascript
const ChatMessageSchema = {
  id: String,           // 消息ID
  content: String,      // 消息内容
  sender: String,       // 发送者 ('user' | 'pet')
  timestamp: Date,      // 发送时间
  type: String,         // 消息类型 ('text' | 'emoji' | 'action')
  read: Boolean         // 是否已读
}

const ChatSessionSchema = {
  id: String,           // 会话ID
  petId: String,        // 宠物ID
  messages: Array,      // 消息列表
  lastMessageTime: Date,// 最后消息时间
  unreadCount: Number   // 未读消息数
}
```

### 用户数据模型
```javascript
const UserDataSchema = {
  openId: String,       // 微信用户标识
  nickname: String,     // 用户昵称
  avatar: String,       // 头像URL
  level: Number,        // 用户等级
  settings: {
    taskReminder: Boolean,    // 任务提醒
    waterReminder: Boolean,   // 饮水提醒
    sleepReminder: Boolean,   // 睡眠提醒
    dataSync: Boolean,        // 数据同步
    theme: String,            // 主题设置 ('auto', 'light', 'dark')
    language: String          // 语言设置 ('zh-CN', 'en-US')
  },
  statistics: {
    loginDays: Number,        // 登录天数
    tasksCompleted: Number,   // 完成任务数
    totalExperience: Number,  // 总经验值
    companionDays: Number     // 陪伴天数
  },
  createdAt: Date,      // 注册时间
  lastLoginAt: Date     // 最后登录时间
}
```

## 错误处理

### 错误类型定义
```javascript
const ErrorTypes = {
  STORAGE_ERROR: 'STORAGE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  DATA_VALIDATION_ERROR: 'DATA_VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR'
}
```

### 错误处理策略
1. **存储错误**: 使用默认数据，提示用户重试
2. **网络错误**: 离线模式运行，数据同步延后
3. **数据验证错误**: 重置为安全默认值，记录错误日志
4. **权限错误**: 引导用户授权，提供降级功能

### 错误处理工具类
```javascript
// utils/errorHandler.js
class ErrorHandler {
  static handle(error, context = '') {
    console.error(`[${context}] Error:`, error)
    
    switch (error.type) {
      case ErrorTypes.STORAGE_ERROR:
        wx.showToast({
          title: '数据保存失败，请重试',
          icon: 'none'
        })
        break
        
      case ErrorTypes.NETWORK_ERROR:
        wx.showToast({
          title: '网络连接异常',
          icon: 'none'
        })
        break
        
      default:
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        })
    }
  }
  
  static async safeExecute(fn, fallback = null) {
    try {
      return await fn()
    } catch (error) {
      this.handle(error)
      return fallback
    }
  }
}
```

## 测试策略

### 单元测试
- **工具类测试**: 数据管理器、任务管理器的核心方法
- **组件测试**: 属性条、任务列表等组件的渲染和交互
- **数据验证测试**: 输入数据的格式和范围验证

### 集成测试
- **页面流程测试**: 主要用户操作流程的端到端测试
- **数据持久化测试**: 数据保存和恢复的完整性测试
- **跨页面通信测试**: 页面间数据传递的准确性测试

### 性能测试
- **启动时间测试**: 小程序冷启动和热启动时间
- **内存使用测试**: 长时间使用的内存占用情况
- **渲染性能测试**: 复杂动画和列表渲染的流畅度

### 兼容性测试
- **设备兼容性**: 不同型号手机的适配测试
- **微信版本兼容性**: 不同微信版本的功能兼容性
- **系统兼容性**: iOS和Android系统的表现差异

### 测试工具和框架
```javascript
// 使用微信开发者工具的测试功能
// test/petDataManager.test.js
const PetDataManager = require('../utils/petDataManager')

describe('PetDataManager', () => {
  let manager
  
  beforeEach(() => {
    manager = new PetDataManager()
    // 清理测试数据
    wx.removeStorageSync('petData')
  })
  
  test('should create default pet data', () => {
    const petData = manager.getPetData()
    expect(petData.level).toBe(1)
    expect(petData.attributes.health).toBe(100)
  })
  
  test('should update attribute correctly', () => {
    const result = manager.updateAttribute('health', 80)
    expect(result.attributes.health).toBe(80)
  })
})
```

### 自动化测试配置
```json
// project.config.json 中的测试配置
{
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": true,
    "newFeature": true,
    "coverView": true,
    "nodeModules": true,
    "autoAudits": true,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    },
    "enableEngineNative": false,
    "bundle": false,
    "useIsolateContext": true,
    "useCompilerModule": true,
    "userConfirmedUseCompilerModuleSwitch": false,
    "packNpmManually": false,
    "packNpmRelationList": [],
    "minifyWXSS": true
  }
}
```