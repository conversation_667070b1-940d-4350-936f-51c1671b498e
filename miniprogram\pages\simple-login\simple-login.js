// simple-login.js
Page({
  data: {
    nickName: '',
    avatarUrl: '',
    debugInfo: ''
  },

  onLoad() {
    // 检查基础库版本
    const systemInfo = wx.getSystemInfoSync();
    console.log('基础库版本:', systemInfo.SDKVersion);
    console.log('微信版本:', systemInfo.version);
    
    this.setData({
      debugInfo: `基础库: ${systemInfo.SDKVersion}, 微信: ${systemInfo.version}`
    });
  },

  getUserProfile() {
    console.log('=== 点击了按钮 ===');
    console.log('准备调用 wx.getUserProfile');
    
    // 检查API是否存在
    if (!wx.getUserProfile) {
      console.error('wx.getUserProfile 不存在！');
      wx.showToast({
        title: 'API不支持',
        icon: 'none'
      });
      return;
    }
    
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: res => {
        console.log('=== getUserProfile 成功 ===');
        console.log('完整响应:', res);
        console.log('用户信息:', res.userInfo);
        console.log('昵称:', res.userInfo.nickName);
        console.log('头像:', res.userInfo.avatarUrl);
        
        const { nickName, avatarUrl } = res.userInfo;
        this.setData({ 
          nickName, 
          avatarUrl,
          debugInfo: this.data.debugInfo + ' | 获取成功'
        });
        
        wx.showToast({
          title: '获取成功',
          icon: 'success'
        });
      },
      fail: err => {
        console.log('=== getUserProfile 失败 ===');
        console.error('错误信息:', err);
        
        this.setData({
          debugInfo: this.data.debugInfo + ' | 获取失败: ' + err.errMsg
        });
        
        wx.showToast({
          title: '获取失败: ' + err.errMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 测试真机预览
  testPreview() {
    wx.showModal({
      title: '真机测试提示',
      content: '请点击开发工具的"预览"按钮，用微信扫码在真机上测试授权功能。开发工具可能不会弹出真实的授权弹窗。\n\n步骤：\n1. 点击开发工具右上角"预览"\n2. 用微信扫描二维码\n3. 在手机上点击授权按钮\n4. 查看是否弹出授权弹窗',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 检查环境信息
  checkEnvironment() {
    const systemInfo = wx.getSystemInfoSync();
    const accountInfo = wx.getAccountInfoSync();

    console.log('=== 环境检查 ===');
    console.log('基础库版本:', systemInfo.SDKVersion);
    console.log('微信版本:', systemInfo.version);
    console.log('小程序AppID:', accountInfo.miniProgram.appId);
    console.log('小程序版本:', accountInfo.miniProgram.version);
    console.log('环境类型:', accountInfo.miniProgram.envVersion);

    const envInfo = `
环境信息：
- 基础库: ${systemInfo.SDKVersion}
- 微信版本: ${systemInfo.version}
- AppID: ${accountInfo.miniProgram.appId}
- 环境: ${accountInfo.miniProgram.envVersion}
- 支持getUserProfile: ${wx.getUserProfile ? '是' : '否'}
    `;

    wx.showModal({
      title: '环境检查',
      content: envInfo,
      showCancel: false
    });
  }
});