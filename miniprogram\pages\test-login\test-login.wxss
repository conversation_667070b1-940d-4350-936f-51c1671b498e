/* pages/test-login/test-login.wxss */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.user-info {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #333;
}

.details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.details text {
  font-size: 24rpx;
  color: #666;
}

.buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.buttons button {
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.buttons button::after {
  border: none;
}

.tips {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  padding: 30rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
  line-height: 1.5;
}