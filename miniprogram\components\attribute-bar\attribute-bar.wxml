<!--components/attribute-bar/attribute-bar.wxml-->
<view class="attribute-bar-container {{animationClass}}" bindtap="onAttributeBarTap">
  <!-- 属性标签和数值 -->
  <view class="attribute-header">
    <view class="attribute-label">
      <image wx:if="{{icon}}" src="{{icon}}" class="attribute-icon"></image>
      <text class="label-text">{{label}}</text>
    </view>
    <text class="attribute-value">{{displayCurrent}}/{{max}}</text>
  </view>
  
  <!-- 进度条容器 -->
  <view class="progress-container">
    <!-- 背景条 -->
    <view class="progress-background"></view>
    
    <!-- 进度条 -->
    <view 
      class="progress-bar" 
      style="width: {{percentage}}%; background-color: {{barColor}};"
    ></view>
    
    <!-- 进度条光效 -->
    <view 
      class="progress-shine" 
      style="width: {{percentage}}%;"
      wx:if="{{percentage > 0}}"
    ></view>
  </view>
  
  <!-- 百分比显示 -->
  <view class="percentage-display">
    <text class="percentage-text">{{percentage.toFixed(0)}}%</text>
  </view>
</view>