/**
 * 日志记录工具类
 * 提供统一的日志记录和管理功能
 */
class Logger {
  constructor() {
    this.logStorageKey = 'appLogs'
    this.maxLogCount = 200
    this.logLevels = {
      DEBUG: 0,
      INFO: 1,
      WARN: 2,
      ERROR: 3
    }
    this.currentLevel = this.logLevels.INFO
  }

  /**
   * 设置日志级别
   */
  static setLevel(level) {
    const logger = new Logger()
    if (typeof level === 'string' && logger.logLevels[level.toUpperCase()] !== undefined) {
      logger.currentLevel = logger.logLevels[level.toUpperCase()]
    } else if (typeof level === 'number') {
      logger.currentLevel = level
    }
  }

  /**
   * 记录调试信息
   */
  static debug(message, data = null) {
    const logger = new Logger()
    logger.log('DEBUG', message, data)
  }

  /**
   * 记录一般信息
   */
  static info(message, data = null) {
    const logger = new Logger()
    logger.log('INFO', message, data)
  }

  /**
   * 记录警告信息
   */
  static warn(message, data = null) {
    const logger = new Logger()
    logger.log('WARN', message, data)
  }

  /**
   * 记录错误信息
   */
  static error(message, error = null) {
    const logger = new Logger()
    logger.log('ERROR', message, error)
  }

  /**
   * 核心日志记录方法
   */
  log(level, message, data = null) {
    const levelValue = this.logLevels[level]
    
    // 检查日志级别
    if (levelValue < this.currentLevel) {
      return
    }

    // 控制台输出
    this.consoleLog(level, message, data)

    // 存储日志
    this.storeLog(level, message, data)
  }

  /**
   * 控制台输出
   */
  consoleLog(level, message, data) {
    const timestamp = new Date().toLocaleString()
    const logMessage = `[${timestamp}] [${level}] ${message}`

    switch (level) {
      case 'DEBUG':
        console.log(logMessage, data)
        break
      case 'INFO':
        console.info(logMessage, data)
        break
      case 'WARN':
        console.warn(logMessage, data)
        break
      case 'ERROR':
        console.error(logMessage, data)
        break
      default:
        console.log(logMessage, data)
    }
  }

  /**
   * 存储日志到本地
   */
  storeLog(level, message, data) {
    try {
      const logs = wx.getStorageSync(this.logStorageKey) || []
      
      const logEntry = {
        id: Date.now() + Math.random(),
        timestamp: new Date().getTime(),
        level,
        message,
        data: this.serializeData(data),
        systemInfo: this.getBasicSystemInfo()
      }

      logs.push(logEntry)

      // 限制日志数量
      if (logs.length > this.maxLogCount) {
        logs.splice(0, logs.length - this.maxLogCount)
      }

      wx.setStorageSync(this.logStorageKey, logs)
    } catch (error) {
      console.error('存储日志失败:', error)
    }
  }

  /**
   * 序列化数据
   */
  serializeData(data) {
    if (data === null || data === undefined) {
      return null
    }

    try {
      if (data instanceof Error) {
        return {
          name: data.name,
          message: data.message,
          stack: data.stack
        }
      }
      
      if (typeof data === 'object') {
        return JSON.parse(JSON.stringify(data))
      }
      
      return data
    } catch (error) {
      return String(data)
    }
  }

  /**
   * 获取基本系统信息
   */
  getBasicSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return {
        platform: systemInfo.platform,
        version: systemInfo.version,
        model: systemInfo.model
      }
    } catch (error) {
      return {
        platform: 'unknown',
        version: 'unknown',
        model: 'unknown'
      }
    }
  }

  /**
   * 获取日志记录
   */
  static getLogs(level = null, limit = 50) {
    try {
      const logger = new Logger()
      const logs = wx.getStorageSync(logger.logStorageKey) || []
      
      let filteredLogs = logs
      if (level) {
        filteredLogs = logs.filter(log => log.level === level.toUpperCase())
      }
      
      return filteredLogs.slice(-limit).reverse()
    } catch (error) {
      console.error('获取日志失败:', error)
      return []
    }
  }

  /**
   * 清理日志
   */
  static clearLogs() {
    try {
      const logger = new Logger()
      wx.removeStorageSync(logger.logStorageKey)
      return true
    } catch (error) {
      console.error('清理日志失败:', error)
      return false
    }
  }

  /**
   * 导出日志
   */
  static exportLogs() {
    try {
      const logs = Logger.getLogs(null, 100)
      const exportData = {
        exportTime: new Date().toISOString(),
        totalCount: logs.length,
        logs: logs
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出日志失败:', error)
      return null
    }
  }

  /**
   * 记录用户操作
   */
  static logUserAction(action, page, data = null) {
    Logger.info(`用户操作: ${action}`, {
      page,
      action,
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 记录性能指标
   */
  static logPerformance(metric, value, unit = 'ms') {
    Logger.info(`性能指标: ${metric}`, {
      metric,
      value,
      unit,
      timestamp: Date.now()
    })
  }

  /**
   * 记录API调用
   */
  static logApiCall(api, params, result, duration) {
    Logger.info(`API调用: ${api}`, {
      api,
      params,
      result: result ? 'success' : 'failed',
      duration,
      timestamp: Date.now()
    })
  }

  /**
   * 获取日志统计
   */
  static getLogStats() {
    try {
      const logs = Logger.getLogs(null, 1000)
      const stats = {
        total: logs.length,
        byLevel: {},
        byHour: {},
        recent24h: 0
      }

      const now = Date.now()
      const oneDayAgo = now - 24 * 60 * 60 * 1000

      logs.forEach(log => {
        // 按级别统计
        stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1
        
        // 按小时统计
        const hour = new Date(log.timestamp).getHours()
        stats.byHour[hour] = (stats.byHour[hour] || 0) + 1
        
        // 最近24小时统计
        if (log.timestamp >= oneDayAgo) {
          stats.recent24h++
        }
      })

      return stats
    } catch (error) {
      console.error('获取日志统计失败:', error)
      return null
    }
  }
}

module.exports = Logger