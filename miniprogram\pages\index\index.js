// index.js
const PetDataManager = require('../../utils/petDataManager')
const TaskManager = require('../../utils/taskManager')

Page({
  data: {
    currentTime: '',
    activeTab: 'overview',
    petData: null,
    petStats: {
      health: 85,
      energy: 72,
      happiness: 95,
      growth: 78,
      experience: 340,
      maxExp: 500,
      level: 8,
      daysWithPet: 18,
      totalExp: 775
    },
    petAnimation: 'idle',
    showFloatingExp: false,
    tasks: [],
    achievements: [
      { name: '健康达人', progress: 80 },
      { name: '运动之星', progress: 45 },
      { name: '时间管理师', progress: 90 }
    ],
    completedTasksCount: 0,
    todayExp: 0,
    topSpacing: 120 // 默认值，会在onLoad中更新
  },
  onLoad: function() {
    // 初始化数据管理器
    this.petDataManager = new PetDataManager()
    this.taskManager = new TaskManager()
    
    this.updateTime();
    this.loadPetData();
    this.loadTasks();
    this.setTopSpacing();
    
    // 每秒更新时间
    this.timeInterval = setInterval(() => {
      this.updateTime();
    }, 1000);
    
    // 每30秒更新一次宠物数据（模拟时间流逝）
    this.petUpdateInterval = setInterval(() => {
      this.updatePetDataRealtime();
    }, 30000);
  },

  // 设置顶部间距，避免与胶囊按钮重叠
  setTopSpacing: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    // 设置合理的顶部间距，通常是状态栏高度加一点间距
    const topSpacing = statusBarHeight + 10;
    
    this.setData({
      topSpacing: topSpacing
    });
  },

  onShow: function() {
    this.loadPetData();
  },

  onUnload: function() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  },

  // 更新当前时间
  updateTime: function() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    this.setData({
      currentTime: timeString
    });
  },

  // 加载宠物数据
  loadPetData: function() {
    const app = getApp();
    const petData = app.getPetData();
    if (petData) {
      this.setData({
        petData: petData,
        petStats: {
          health: petData.attributes.health,
          energy: petData.attributes.energy,
          happiness: petData.attributes.intimacy,
          experience: petData.experience,
          maxExp: petData.maxExperience,
          level: petData.level,
          daysWithPet: petData.totalDays || 18,
          totalExp: petData.totalExperience || 775
        }
      });
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 完成任务
  completeTask: function(e) {
    const taskId = e.currentTarget.dataset.id;
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId && !task.completed) {
        task.completed = true;
        // 增加经验值
        this.addExperience(task.reward);
        wx.showToast({
          title: `完成任务！获得${task.reward}经验`,
          icon: 'success'
        });
      }
      return task;
    });
    
    const completedCount = tasks.filter(t => t.completed).length;
    this.setData({
      tasks: tasks,
      completedTasksCount: completedCount
    });
  },

  // 属性条点击事件处理
  onAttributeTap: function(e) {
    const { label, current, max, percentage } = e.detail
    
    // 显示属性详情
    wx.showModal({
      title: `${label}详情`,
      content: `当前值: ${current}/${max} (${percentage.toFixed(1)}%)`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 宠物交互事件处理
  onPetInteraction: function(e) {
    const { type, intimacyIncrease } = e.detail
    
    try {
      // 增加亲密度
      if (intimacyIncrease > 0) {
        const petData = this.petDataManager.updateAttribute('intimacy', 
          this.data.petData.attributes.intimacy + intimacyIncrease)
        
        this.setData({
          petData: petData
        })
      }
      
      // 触发宠物动画效果
      this.triggerPetAnimation(type)
      
    } catch (error) {
      console.error('宠物交互失败:', error)
    }
  },

  // 触发宠物动画效果
  triggerPetAnimation: function(type) {
    this.setData({
      petAnimation: type,
      showFloatingExp: true
    })

    // 重置动画状态
    setTimeout(() => {
      this.setData({
        petAnimation: 'idle',
        showFloatingExp: false
      })
    }, 2000)
  },

  // 宠物互动按钮处理
  handlePetInteraction: function(e) {
    const action = e.currentTarget.dataset.action;
    const expGain = 25;
    
    try {
      // 根据不同动作更新不同属性
      let message = '';
      let newStats = { ...this.data.petStats };
      
      switch(action) {
        case 'feed':
          newStats.health = Math.min(100, newStats.health + 10);
          message = '喂食成功！健康值+10';
          break;
        case 'play':
          newStats.happiness = Math.min(100, newStats.happiness + 15);
          message = '互动成功！开心值+15';
          break;
        case 'walk':
          newStats.energy = Math.min(100, newStats.energy + 12);
          message = '散步成功！活力值+12';
          break;
        case 'game':
          newStats.happiness = Math.min(100, newStats.happiness + 8);
          newStats.energy = Math.max(0, newStats.energy - 5);
          message = '游戏成功！开心值+8，活力值-5';
          break;
      }
      
      // 增加经验值
      newStats.experience = Math.min(newStats.maxExp, newStats.experience + expGain);
      
      // 更新页面数据
      this.setData({
        petStats: newStats,
        petAnimation: action,
        showFloatingExp: true
      });

      // 显示反馈提示
      wx.showToast({
        title: message,
        icon: 'success',
        duration: 1500
      });

      // 重置动画状态
      setTimeout(() => {
        this.setData({
          petAnimation: 'idle',
          showFloatingExp: false
        });
      }, 2000);
      
    } catch (error) {
      console.error('宠物互动失败:', error);
      wx.showToast({
        title: '互动失败',
        icon: 'none'
      });
    }
  },

  // 页面跳转逻辑
  goToChat: function() {
    wx.navigateTo({
      url: '/pages/chat/index'
    });
  },

  // 跳转到个人资料页面
  goToProfile: function() {
    wx.navigateTo({
      url: '/pages/profile/index'
    });
  },

  // 跳转到任务页面（如果有独立任务页面）
  goToTasks: function() {
    // 切换到任务标签页
    this.setData({
      activeTab: 'tasks'
    });
  },

  // 跳转到成就页面
  goToAchievements: function() {
    // 切换到成就标签页
    this.setData({
      activeTab: 'achievements'
    });
  },

  // 跳转到设置页面
  goToSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/index'
    });
  },

  // 加载任务数据
  loadTasks: function() {
    // 模拟任务数据
    const tasks = [
      { id: 1, name: '每日步行', description: '走8000步，保持身体健康', reward: 20, completed: false, icon: '🚶', color: 'bg-yellow' },
      { id: 2, name: '喝水打卡', description: '每天喝8杯水，保持身体水分', reward: 15, completed: false, icon: '💧', color: 'bg-blue' },
      { id: 3, name: '早睡早起', description: '23点前睡觉，7点前起床', reward: 25, completed: false, icon: '😴', color: 'bg-green' },
      { id: 4, name: '健康饮食', description: '多吃蔬菜水果，少吃垃圾食品', reward: 18, completed: false, icon: '🥗', color: 'bg-purple' }
    ];
    
    const completedCount = tasks.filter(t => t.completed).length;
    this.setData({
      tasks: tasks,
      completedTasksCount: completedCount
    });
  },

  // 实时更新宠物数据
  updatePetDataRealtime: function() {
    // 模拟时间流逝对宠物属性的影响
    let newStats = { ...this.data.petStats };
    
    // 随时间缓慢下降
    newStats.health = Math.max(0, newStats.health - 1);
    newStats.energy = Math.max(0, newStats.energy - 2);
    newStats.happiness = Math.max(0, newStats.happiness - 1);
    
    this.setData({
      petStats: newStats
    });
  }
});
