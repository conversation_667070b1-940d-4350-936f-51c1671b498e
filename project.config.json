{"description": "项目配置文件", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "bundle": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true}, "compileType": "miniprogram", "libVersion": "2.27.0", "appid": "wx93d681b2a96118bf", "projectname": "virtual-pet-miniprogram", "miniprogramRoot": "miniprogram/", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "cloudfunctionRoot": "cloudfunctions/"}