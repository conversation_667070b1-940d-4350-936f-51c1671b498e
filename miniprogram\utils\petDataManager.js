/**
 * 宠物数据管理器
 * 负责宠物数据的增删改查、属性值更新和经验值计算
 */
const { ErrorHandler } = require('./errorHandler')
const SecureStorage = require('./secureStorage')
const Logger = require('./logger')

class PetDataManager {
  constructor() {
    this.storageKey = 'petData'
    this.defaultPetData = this.getDefaultPetData()
  }

  /**
   * 获取默认宠物数据
   */
  getDefaultPetData() {
    return {
      id: 'pet_' + Date.now(),
      name: '小绿',
      level: 1,
      experience: 0,
      maxExperience: 100,
      attributes: {
        health: 100,    // 健康值
        energy: 100,    // 活力值
        intimacy: 50    // 亲密度
      },
      status: '很开心',
      lastUpdateTime: new Date().getTime(),
      totalDays: 1,
      totalExperience: 0,
      createdAt: new Date().getTime()
    }
  }

  /**
   * 获取宠物数据
   */
  getPetData() {
    return ErrorHandler.safeExecuteSync(() => {
      const data = SecureStorage.getItem(this.storageKey, null, true)
      if (!data || !this.validatePetData(data)) {
        Logger.warn('宠物数据无效，重置为默认数据')
        return this.resetPetData()
      }
      return data
    }, this.resetPetData(), 'getPetData')
  }

  /**
   * 保存宠物数据
   */
  savePetData(data) {
    return ErrorHandler.safeExecuteSync(() => {
      if (!this.validatePetData(data)) {
        throw new Error('宠物数据验证失败')
      }
      
      data.lastUpdateTime = new Date().getTime()
      const success = SecureStorage.setItem(this.storageKey, data, true)
      
      if (success) {
        Logger.debug('宠物数据保存成功', { level: data.level, health: data.attributes.health })
      }
      
      return success
    }, false, 'savePetData')
  }

  /**
   * 重置宠物数据为默认值
   */
  resetPetData() {
    const defaultData = this.getDefaultPetData()
    this.savePetData(defaultData)
    return defaultData
  }

  /**
   * 验证宠物数据完整性
   */
  validatePetData(data) {
    if (!data || typeof data !== 'object') return false
    
    const requiredFields = ['id', 'name', 'level', 'experience', 'maxExperience', 'attributes']
    for (let field of requiredFields) {
      if (!(field in data)) return false
    }
    
    const requiredAttributes = ['health', 'energy', 'intimacy']
    for (let attr of requiredAttributes) {
      if (!(attr in data.attributes)) return false
    }
    
    return true
  }

  /**
   * 更新单个属性值
   */
  updateAttribute(attribute, value) {
    const petData = this.getPetData()
    
    if (!petData.attributes.hasOwnProperty(attribute)) {
      throw new Error(`未知的属性: ${attribute}`)
    }
    
    // 确保属性值在0-100范围内
    const newValue = Math.max(0, Math.min(100, value))
    petData.attributes[attribute] = newValue
    
    // 更新宠物状态
    this.updatePetStatus(petData)
    
    this.savePetData(petData)
    return petData
  }

  /**
   * 批量更新属性值
   */
  updateAttributes(attributes) {
    const petData = this.getPetData()
    
    for (let [attribute, value] of Object.entries(attributes)) {
      if (petData.attributes.hasOwnProperty(attribute)) {
        petData.attributes[attribute] = Math.max(0, Math.min(100, value))
      }
    }
    
    this.updatePetStatus(petData)
    this.savePetData(petData)
    return petData
  }

  /**
   * 增加经验值
   */
  addExperience(exp) {
    const petData = this.getPetData()
    
    if (exp <= 0) return petData
    
    petData.experience += exp
    petData.totalExperience += exp
    
    // 检查是否升级
    let leveledUp = false
    while (petData.experience >= petData.maxExperience) {
      petData.experience -= petData.maxExperience
      petData.level++
      petData.maxExperience = this.calculateMaxExp(petData.level)
      leveledUp = true
    }
    
    this.savePetData(petData)
    
    return {
      petData,
      leveledUp,
      expGained: exp
    }
  }

  /**
   * 计算升级所需经验值
   */
  calculateMaxExp(level) {
    return Math.floor(100 * Math.pow(1.2, level - 1))
  }

  /**
   * 更新宠物状态描述
   */
  updatePetStatus(petData) {
    const { health, energy, intimacy } = petData.attributes
    const avgAttribute = (health + energy + intimacy) / 3
    
    if (avgAttribute >= 80) {
      petData.status = '非常开心'
    } else if (avgAttribute >= 60) {
      petData.status = '很开心'
    } else if (avgAttribute >= 40) {
      petData.status = '还不错'
    } else if (avgAttribute >= 20) {
      petData.status = '有点累'
    } else {
      petData.status = '需要照顾'
    }
  }

  /**
   * 计算陪伴天数
   */
  updateCompanionDays() {
    const petData = this.getPetData()
    const now = new Date().getTime()
    const daysDiff = Math.floor((now - petData.createdAt) / (1000 * 60 * 60 * 24))
    petData.totalDays = Math.max(1, daysDiff + 1)
    
    this.savePetData(petData)
    return petData.totalDays
  }

  /**
   * 获取宠物统计信息
   */
  getPetStats() {
    const petData = this.getPetData()
    this.updateCompanionDays()
    
    return {
      level: petData.level,
      totalDays: petData.totalDays,
      totalExperience: petData.totalExperience,
      currentExp: petData.experience,
      maxExp: petData.maxExperience,
      expProgress: (petData.experience / petData.maxExperience * 100).toFixed(1)
    }
  }

  /**
   * 模拟时间流逝对属性的影响
   */
  simulateTimeDecay() {
    const petData = this.getPetData()
    const now = new Date().getTime()
    const lastUpdate = petData.lastUpdateTime || now
    const hoursPassed = (now - lastUpdate) / (1000 * 60 * 60)
    
    if (hoursPassed >= 1) {
      // 每小时属性自然衰减
      const decayRate = Math.min(hoursPassed * 2, 10) // 最多衰减10点
      
      petData.attributes.health = Math.max(0, petData.attributes.health - decayRate)
      petData.attributes.energy = Math.max(0, petData.attributes.energy - decayRate * 1.5)
      
      this.updatePetStatus(petData)
      this.savePetData(petData)
    }
    
    return petData
  }
}

module.exports = PetDataManager