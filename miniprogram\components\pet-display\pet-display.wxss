/* components/pet-display/pet-display.wxss */
.pet-display-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
  border-radius: 20rpx;
  margin: 20rpx;
  position: relative;
  min-height: 400rpx;
}

.pet-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
}

/* 等级标签 */
.level-badge {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background: #ff6b6b;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 4rpx 8rpx rgba(255, 107, 107, 0.3);
}

/* 宠物形象容器 */
.pet-image {
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 40rpx 0;
  cursor: pointer;
}

.pet-sprite {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.1));
}

/* 宠物信息 */
.pet-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
}

.pet-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d5a27;
  margin-bottom: 8rpx;
}

.pet-status {
  font-size: 26rpx;
  color: #5a8a52;
  opacity: 0.8;
}

/* 互动提示 */
.interaction-hint {
  margin-top: 20rpx;
  opacity: 0.6;
}

.hint-text {
  font-size: 22rpx;
  color: #2d5a27;
}

/* 升级特效 */
.levelup-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
}

.levelup-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffd700;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
  animation: levelup-bounce 2s ease-in-out;
}

/* 动画效果 */
@keyframes levelup-bounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 宠物状态动画 */
.pet-happy {
  animation: happy-bounce 2s ease-in-out infinite;
}

.pet-excited {
  animation: excited-shake 1s ease-in-out infinite;
}

.pet-tired {
  animation: tired-sway 3s ease-in-out infinite;
}

.pet-normal {
  animation: normal-float 4s ease-in-out infinite;
}

.pet-tap {
  animation: tap-scale 0.5s ease-in-out;
}

.pet-levelup {
  animation: levelup-glow 2s ease-in-out;
}

@keyframes happy-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes excited-shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5rpx);
  }
  75% {
    transform: translateX(5rpx);
  }
}

@keyframes tired-sway {
  0%, 100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(2deg);
  }
}

@keyframes normal-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
}

@keyframes tap-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes levelup-glow {
  0%, 100% {
    filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.1));
  }
  50% {
    filter: drop-shadow(0 8rpx 16rpx rgba(255, 215, 0, 0.6));
  }
}