/* components/attribute-bar/attribute-bar.wxss */
.attribute-bar-container {
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  margin: 16rpx 0;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.attribute-bar-container:active {
  transform: scale(0.98);
}

/* 属性头部 */
.attribute-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.attribute-label {
  display: flex;
  align-items: center;
}

.attribute-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.attribute-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #666;
}

/* 进度条容器 */
.progress-container {
  position: relative;
  height: 16rpx;
  margin-bottom: 12rpx;
  overflow: hidden;
}

/* 背景条 */
.progress-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border-radius: 8rpx;
}

/* 进度条 */
.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.8s ease, background-color 0.3s ease;
  background: linear-gradient(90deg, currentColor 0%, currentColor 100%);
}

/* 进度条光效 */
.progress-shine {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 8rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%
  );
  animation: shine 2s ease-in-out infinite;
}

/* 百分比显示 */
.percentage-display {
  display: flex;
  justify-content: flex-end;
}

.percentage-text {
  font-size: 22rpx;
  color: #999;
  font-weight: 500;
}

/* 动画效果 */
@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.bar-tap {
  animation: bar-tap-effect 0.3s ease;
}

@keyframes bar-tap-effect {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

/* 不同状态的颜色主题 */
.attribute-bar-container[data-status="excellent"] .progress-bar {
  background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
}

.attribute-bar-container[data-status="good"] .progress-bar {
  background: linear-gradient(90deg, #8BC34A 0%, #9CCC65 100%);
}

.attribute-bar-container[data-status="normal"] .progress-bar {
  background: linear-gradient(90deg, #FFC107 0%, #FFD54F 100%);
}

.attribute-bar-container[data-status="warning"] .progress-bar {
  background: linear-gradient(90deg, #FF9800 0%, #FFB74D 100%);
}

.attribute-bar-container[data-status="danger"] .progress-bar {
  background: linear-gradient(90deg, #F44336 0%, #EF5350 100%);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .attribute-bar-container {
    padding: 20rpx;
    margin: 12rpx 0;
  }
  
  .label-text {
    font-size: 26rpx;
  }
  
  .attribute-value {
    font-size: 24rpx;
  }
  
  .progress-container {
    height: 14rpx;
  }
}