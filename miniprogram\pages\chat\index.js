// chat.js
Page({
  data: {
    inputText: '',
    scrollTop: 0,
    toView: '',
    topSpacing: 120,
    messages: [
      {
        id: 1,
        type: 'pet',
        content: '你好！我是小橙，很高兴见到你！🦊',
        time: '10:30'
      },
      {
        id: 2,
        type: 'pet',
        content: '今天过得怎么样？有什么想和我分享的吗？',
        time: '10:30'
      }
    ]
  },

  onLoad: function() {
    this.setTopSpacing();
    this.scrollToBottom();
  },

  // 设置顶部间距
  setTopSpacing: function() {
    const systemInfo = wx.getSystemInfoSync();
    const capsuleInfo = wx.getMenuButtonBoundingClientRect();
    const topSpacing = capsuleInfo.top;
    
    this.setData({
      topSpacing: topSpacing
    });
  },

  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 发送消息
  sendMessage: function() {
    const inputText = this.data.inputText.trim();
    if (!inputText) return;

    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputText,
      time: timeString
    };

    const messages = [...this.data.messages, userMessage];
    
    this.setData({
      messages: messages,
      inputText: ''
    });

    // 滚动到底部
    this.scrollToBottom();

    // 模拟宠物回复
    setTimeout(() => {
      this.generatePetReply(inputText);
    }, 1000);
  },

  // 生成宠物回复
  generatePetReply: function(userInput) {
    const replies = [
      '哇，听起来很有趣呢！🌟',
      '我明白你的感受，继续加油哦！💪',
      '真的吗？告诉我更多吧！😊',
      '你真棒！我为你感到骄傲！🎉',
      '嗯嗯，我在认真听呢！👂',
      '这让我想起了一个有趣的故事...',
      '你的想法很棒！我们一起努力吧！',
      '哈哈，你总是能让我开心！😄'
    ];

    const randomReply = replies[Math.floor(Math.random() * replies.length)];
    
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    const petMessage = {
      id: Date.now() + 1,
      type: 'pet',
      content: randomReply,
      time: timeString
    };

    const messages = [...this.data.messages, petMessage];
    
    this.setData({
      messages: messages
    });

    this.scrollToBottom();
  },

  // 滚动到底部
  scrollToBottom: function() {
    setTimeout(() => {
      this.setData({
        toView: `message-${this.data.messages.length - 1}`
      });
    }, 100);
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
});