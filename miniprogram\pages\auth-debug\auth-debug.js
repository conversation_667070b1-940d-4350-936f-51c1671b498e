// pages/auth-debug/auth-debug.js
Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    debugLogs: [],
    systemInfo: {},
    accountInfo: {},
    testResults: {}
  },

  onLoad() {
    this.initDebugInfo();
  },

  // 初始化调试信息
  initDebugInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const accountInfo = wx.getAccountInfoSync();
      
      this.setData({
        systemInfo,
        accountInfo
      });
      
      this.addLog('页面加载完成');
      this.addLog(`基础库版本: ${systemInfo.SDKVersion}`);
      this.addLog(`微信版本: ${systemInfo.version}`);
      this.addLog(`AppID: ${accountInfo.miniProgram.appId}`);
      this.addLog(`环境: ${accountInfo.miniProgram.envVersion}`);
      this.addLog(`支持getUserProfile: ${wx.getUserProfile ? '是' : '否'}`);
      
    } catch (error) {
      this.addLog(`初始化失败: ${error.message}`);
    }
  },

  // 添加日志
  addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logs = this.data.debugLogs;
    logs.unshift(`[${timestamp}] ${message}`);
    
    // 只保留最近20条日志
    if (logs.length > 20) {
      logs.pop();
    }
    
    this.setData({ debugLogs: logs });
    console.log(`[AUTH-DEBUG] ${message}`);
  },

  // 测试不同的 desc 长度
  testDescLength() {
    const testDescs = [
      '用于完善会员资料',           // 8个字符
      '用于完善用户资料',           // 8个字符
      '获取用户信息',               // 6个字符
      '完善资料',                   // 4个字符
      '用于展示用户头像和昵称信息', // 12个字符
      '用于完善用户个人资料信息'    // 11个字符
    ];

    this.addLog('=== 测试不同 desc 长度 ===');

    testDescs.forEach((desc, index) => {
      this.addLog(`测试 ${index + 1}: "${desc}" (${desc.length}个字符)`);
    });

    // 使用第一个进行实际测试
    this.testGetUserProfileWithDesc(testDescs[0]);
  },

  // 使用指定 desc 测试 getUserProfile
  testGetUserProfileWithDesc(desc) {
    this.addLog(`使用 desc: "${desc}" (${desc.length}个字符)`);

    wx.getUserProfile({
      desc: desc,
      success: (res) => {
        this.addLog('✅ getUserProfile 调用成功');
        this.addLog(`获取到用户信息: ${JSON.stringify(res.userInfo)}`);
        this.addLog(`昵称: ${res.userInfo.nickName}`);
        this.addLog(`头像: ${res.userInfo.avatarUrl}`);

        // 检查是否是降级处理
        if (res.userInfo.is_demote) {
          this.addLog('⚠️ 检测到 is_demote: true - 这是模拟数据');
          this.addLog('🔥 这表明当前在开发工具环境中，需要真机测试');
          this.addLog('📱 请点击"预览"按钮，在真机上测试获取真实用户信息');

          this.setData({
            'testResults.getUserProfile': '成功(模拟数据)'
          });

          wx.showModal({
            title: '获取到模拟数据',
            content: '检测到 is_demote: true，这是开发工具的模拟数据。\n\n要获取真实用户信息，请：\n1. 点击开发工具"预览"按钮\n2. 用微信扫码在真机测试\n3. 在手机上点击授权按钮',
            showCancel: false,
            confirmText: '知道了'
          });
        } else {
          this.addLog('🎉 获取到真实用户信息！');
          this.setData({
            'testResults.getUserProfile': '成功(真实数据)'
          });

          wx.showToast({
            title: '获取真实信息成功',
            icon: 'success'
          });
        }

        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        });
      },
      fail: (err) => {
        this.addLog('❌ getUserProfile 调用失败');
        this.addLog(`错误信息: ${err.errMsg}`);
        this.addLog(`错误码: ${err.errno || '无'}`);

        this.setData({
          'testResults.getUserProfile': `失败: ${err.errMsg}`
        });

        if (err.errMsg.includes('cancel')) {
          this.addLog('用户取消了授权');
          wx.showToast({
            title: '用户取消授权',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: `授权失败: ${err.errMsg}`,
            icon: 'none',
            duration: 3000
          });
        }
      }
    });
  },

  // 测试 getUserProfile
  testGetUserProfile() {
    this.addLog('=== 开始测试 getUserProfile ===');
    this.addLog('用户点击了授权按钮');
    
    // 检查API是否存在
    if (!wx.getUserProfile) {
      this.addLog('❌ wx.getUserProfile API 不存在');
      wx.showToast({
        title: 'API不支持',
        icon: 'none'
      });
      return;
    }
    
    this.addLog('✅ wx.getUserProfile API 存在');
    this.addLog('准备调用 wx.getUserProfile...');
    
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        this.addLog('✅ getUserProfile 调用成功');
        this.addLog(`获取到用户信息: ${JSON.stringify(res.userInfo)}`);
        this.addLog(`昵称: ${res.userInfo.nickName}`);
        this.addLog(`头像: ${res.userInfo.avatarUrl}`);

        // 检查是否是降级处理
        if (res.userInfo.is_demote) {
          this.addLog('⚠️ 检测到 is_demote: true - 这是模拟数据');
          this.addLog('🔥 这表明当前在开发工具环境中，需要真机测试');
          this.addLog('📱 请点击"预览"按钮，在真机上测试获取真实用户信息');

          this.setData({
            'testResults.getUserProfile': '成功(模拟数据)'
          });

          wx.showModal({
            title: '获取到模拟数据',
            content: '检测到 is_demote: true，这是开发工具的模拟数据。\n\n要获取真实用户信息，请：\n1. 点击开发工具"预览"按钮\n2. 用微信扫码在真机测试\n3. 在手机上点击授权按钮',
            showCancel: false,
            confirmText: '知道了'
          });
        } else {
          this.addLog('🎉 获取到真实用户信息！');
          this.setData({
            'testResults.getUserProfile': '成功(真实数据)'
          });

          wx.showToast({
            title: '获取真实信息成功',
            icon: 'success'
          });
        }

        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        });
      },
      fail: (err) => {
        this.addLog('❌ getUserProfile 调用失败');
        this.addLog(`错误信息: ${err.errMsg}`);
        this.addLog(`错误码: ${err.errno || '无'}`);
        
        this.setData({
          'testResults.getUserProfile': `失败: ${err.errMsg}`
        });
        
        if (err.errMsg.includes('cancel')) {
          this.addLog('用户取消了授权');
          wx.showToast({
            title: '用户取消授权',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: `授权失败: ${err.errMsg}`,
            icon: 'none',
            duration: 3000
          });
        }
      }
    });
  },

  // 测试基础登录
  testBasicLogin() {
    this.addLog('=== 开始测试基础登录 ===');
    
    wx.login({
      success: (res) => {
        this.addLog('✅ wx.login 成功');
        this.addLog(`获取到 code: ${res.code}`);
        this.setData({
          'testResults.login': '成功'
        });
      },
      fail: (err) => {
        this.addLog('❌ wx.login 失败');
        this.addLog(`错误信息: ${err.errMsg}`);
        this.setData({
          'testResults.login': `失败: ${err.errMsg}`
        });
      }
    });
  },

  // 清空日志
  clearLogs() {
    this.setData({
      debugLogs: [],
      testResults: {}
    });
    this.addLog('日志已清空');
  },

  // 复制日志
  copyLogs() {
    const logs = this.data.debugLogs.join('\n');
    wx.setClipboardData({
      data: logs,
      success: () => {
        wx.showToast({
          title: '日志已复制',
          icon: 'success'
        });
      }
    });
  },

  // 真机测试说明
  showRealDeviceGuide() {
    wx.showModal({
      title: '真机测试指南',
      content: `开发工具可能不会弹出真实授权弹窗，请按以下步骤在真机测试：

1. 点击开发工具右上角"预览"按钮
2. 用微信扫描生成的二维码
3. 在手机上打开小程序
4. 点击"测试授权"按钮
5. 查看是否弹出授权弹窗

注意：确保小程序已发布或设置为体验版`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 检查AppID状态
  checkAppIdStatus() {
    const accountInfo = wx.getAccountInfoSync();
    const appId = accountInfo.miniProgram.appId;

    this.addLog('=== AppID状态检查 ===');
    this.addLog(`当前AppID: ${appId}`);

    let statusInfo = '';
    let recommendations = '';

    if (appId.startsWith('wxe')) {
      statusInfo = '🔥 检测到测试号AppID';
      recommendations = `测试号限制：
1. 无法获取真实用户信息
2. getUserProfile 永远返回模拟数据
3. is_demote 永远为 true

解决方案：
1. 注册正式小程序账号
2. 获取正式的小程序AppID
3. 替换 project.config.json 中的 appid`;

    } else if (appId === 'wx93d681b2a96118bf') {
      statusInfo = '⚠️ 当前使用的AppID需要验证';
      recommendations = `请确认：
1. 这是否为正式注册的小程序AppID？
2. 小程序是否已通过审核？
3. 小程序是否已发布或设置为体验版？

如果是测试AppID，请：
1. 登录 mp.weixin.qq.com
2. 查看小程序状态
3. 确保小程序已发布`;

    } else {
      statusInfo = '✅ 正式小程序AppID';
      recommendations = `AppID看起来正常，如果仍然降级，请检查：
1. 小程序发布状态
2. 用户授权权限
3. 微信版本兼容性`;
    }

    this.addLog(statusInfo);
    this.addLog('建议: ' + recommendations);

    wx.showModal({
      title: 'AppID状态检查',
      content: `${statusInfo}\n\n${recommendations}`,
      showCancel: false,
      confirmText: '知道了'
    });
  }
});
