/**
 * 错误处理器
 * 处理各种异常情况，提供错误恢复机制
 */

// 错误类型定义
const ErrorTypes = {
  STORAGE_ERROR: 'STORAGE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  DATA_VALIDATION_ERROR: 'DATA_VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  USER_ERROR: 'USER_ERROR'
}

class ErrorHandler {
  constructor() {
    this.logStorageKey = 'errorLogs'
    this.maxLogCount = 100
  }

  /**
   * 处理错误
   */
  static handle(error, context = '', showToast = true) {
    const handler = new ErrorHandler()
    return handler.handleError(error, context, showToast)
  }

  /**
   * 处理错误的核心方法
   */
  handleError(error, context = '', showToast = true) {
    // 记录错误日志
    this.logError(error, context)
    
    // 根据错误类型处理
    const errorInfo = this.parseError(error)
    
    if (showToast) {
      this.showErrorToast(errorInfo)
    }
    
    // 执行错误恢复策略
    this.executeRecoveryStrategy(errorInfo, context)
    
    return errorInfo
  }

  /**
   * 解析错误信息
   */
  parseError(error) {
    let errorType = ErrorTypes.SYSTEM_ERROR
    let message = '操作失败，请重试'
    let recoverable = true
    
    if (typeof error === 'string') {
      message = error
      errorType = ErrorTypes.USER_ERROR
    } else if (error && typeof error === 'object') {
      message = error.message || error.errMsg || message
      
      // 根据错误信息判断类型
      if (message.includes('storage') || message.includes('存储')) {
        errorType = ErrorTypes.STORAGE_ERROR
        message = '数据保存失败，请重试'
      } else if (message.includes('network') || message.includes('网络')) {
        errorType = ErrorTypes.NETWORK_ERROR
        message = '网络连接异常，请检查网络'
      } else if (message.includes('permission') || message.includes('权限')) {
        errorType = ErrorTypes.PERMISSION_ERROR
        message = '权限不足，请授权后重试'
      } else if (message.includes('validation') || message.includes('验证')) {
        errorType = ErrorTypes.DATA_VALIDATION_ERROR
        message = '数据格式错误'
      }
    }
    
    return {
      type: errorType,
      message,
      originalError: error,
      timestamp: new Date().getTime(),
      recoverable
    }
  }

  /**
   * 显示错误提示
   */
  showErrorToast(errorInfo) {
    const icon = errorInfo.recoverable ? 'none' : 'error'
    
    wx.showToast({
      title: errorInfo.message,
      icon: icon,
      duration: 2000
    })
  }

  /**
   * 执行错误恢复策略
   */
  executeRecoveryStrategy(errorInfo, context) {
    switch (errorInfo.type) {
      case ErrorTypes.STORAGE_ERROR:
        this.handleStorageError(context)
        break
        
      case ErrorTypes.DATA_VALIDATION_ERROR:
        this.handleDataValidationError(context)
        break
        
      case ErrorTypes.NETWORK_ERROR:
        this.handleNetworkError(context)
        break
        
      case ErrorTypes.PERMISSION_ERROR:
        this.handlePermissionError(context)
        break
        
      default:
        // 默认恢复策略
        break
    }
  }

  /**
   * 处理存储错误
   */
  handleStorageError(context) {
    try {
      // 尝试清理部分存储空间
      const storageInfo = wx.getStorageInfoSync()
      if (storageInfo.limitSize - storageInfo.currentSize < 1024) {
        this.cleanupStorage()
      }
    } catch (e) {
      console.error('存储错误恢复失败:', e)
    }
  }

  /**
   * 处理数据验证错误
   */
  handleDataValidationError(context) {
    if (context.includes('pet')) {
      // 重置宠物数据
      try {
        const PetDataManager = require('./petDataManager')
        const petManager = new PetDataManager()
        petManager.resetPetData()
      } catch (e) {
        console.error('宠物数据重置失败:', e)
      }
    } else if (context.includes('task')) {
      // 重置任务数据
      try {
        const TaskManager = require('./taskManager')
        const taskManager = new TaskManager()
        taskManager.resetDailyTasks()
      } catch (e) {
        console.error('任务数据重置失败:', e)
      }
    }
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(context) {
    // 启用离线模式标识
    wx.setStorageSync('offlineMode', true)
    
    // 设置重试机制
    setTimeout(() => {
      wx.removeStorageSync('offlineMode')
    }, 30000) // 30秒后重新尝试网络操作
  }

  /**
   * 处理权限错误
   */
  handlePermissionError(context) {
    // 引导用户重新授权
    wx.showModal({
      title: '权限不足',
      content: '需要相关权限才能正常使用功能，是否重新授权？',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting()
        }
      }
    })
  }

  /**
   * 记录错误日志
   */
  logError(error, context) {
    try {
      const logs = wx.getStorageSync(this.logStorageKey) || []
      
      const logEntry = {
        timestamp: new Date().getTime(),
        context,
        error: {
          message: error.message || error.errMsg || error.toString(),
          stack: error.stack || '',
          type: typeof error
        },
        systemInfo: this.getSystemInfo()
      }
      
      logs.push(logEntry)
      
      // 限制日志数量
      if (logs.length > this.maxLogCount) {
        logs.splice(0, logs.length - this.maxLogCount)
      }
      
      wx.setStorageSync(this.logStorageKey, logs)
    } catch (e) {
      console.error('记录错误日志失败:', e)
    }
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      return wx.getSystemInfoSync()
    } catch (e) {
      return {
        platform: 'unknown',
        version: 'unknown'
      }
    }
  }

  /**
   * 清理存储空间
   */
  cleanupStorage() {
    try {
      // 清理错误日志
      const logs = wx.getStorageSync(this.logStorageKey) || []
      if (logs.length > 50) {
        wx.setStorageSync(this.logStorageKey, logs.slice(-50))
      }
      
      // 清理过期的任务历史
      const taskHistory = wx.getStorageSync('taskHistory') || []
      if (taskHistory.length > 15) {
        wx.setStorageSync('taskHistory', taskHistory.slice(-15))
      }
      
      console.log('存储空间清理完成')
    } catch (e) {
      console.error('清理存储空间失败:', e)
    }
  }

  /**
   * 安全执行函数
   */
  static async safeExecute(fn, fallback = null, context = '') {
    try {
      return await fn()
    } catch (error) {
      ErrorHandler.handle(error, context)
      return fallback
    }
  }

  /**
   * 安全执行同步函数
   */
  static safeExecuteSync(fn, fallback = null, context = '') {
    try {
      return fn()
    } catch (error) {
      ErrorHandler.handle(error, context)
      return fallback
    }
  }

  /**
   * 获取错误日志
   */
  static getErrorLogs(limit = 50) {
    try {
      const handler = new ErrorHandler()
      const logs = wx.getStorageSync(handler.logStorageKey) || []
      return logs.slice(-limit)
    } catch (e) {
      return []
    }
  }

  /**
   * 清理错误日志
   */
  static clearErrorLogs() {
    try {
      const handler = new ErrorHandler()
      wx.removeStorageSync(handler.logStorageKey)
      return true
    } catch (e) {
      return false
    }
  }
}

module.exports = {
  ErrorHandler,
  ErrorTypes
}