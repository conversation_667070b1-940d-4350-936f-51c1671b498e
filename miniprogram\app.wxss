/**app.wxss**/
/* 全局样式 */
page {
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #333;
  min-height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 20rpx;
}

/* 按钮重置 */
button {
  background: initial;
  border: none;
  outline: none;
  font-size: inherit;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-body {
  padding: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.card-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin: 10rpx 0;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-health {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.progress-energy {
  
background: linear-gradient(90deg, #FF9800, #FFC107);
}

.progress-intimacy {
  background: linear-gradient(90deg, #E91E63, #F06292);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: #ffffff;
}

.btn-primary:active {
  background: linear-gradient(135deg, #45a049, #3d8b40);
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
}

.btn-secondary:active {
  background: #e0e0e0;
}

.btn-small {
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

/* 文本样式 */
.text-primary {
  color: #4CAF50;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.mt-10 {
  margin-top: 10rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.ml-10 {
  margin-left: 10rpx;
}

.mr-10 {
  margin-right: 10rpx;
}

/* 宠物状态样式 */
.pet-status {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #ffffff;
}

.status-happy {
  background: #4CAF50;
}

.status-normal {
  background: #FF9800;
}

.status-tired {
  background: #F44336;
}

.status-sleeping {
  background: #9C27B0;
}