// pages/login/login.js
Page({
  data: {
    topSpacing: 120,
    avatarUrl: '',
    nickName: ''
  },

  onLoad: function (options) {
    this.setTopSpacing();
  },

  // 设置顶部间距
  setTopSpacing: function () {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    const capsuleInfo = wx.getMenuButtonBoundingClientRect();
    const topSpacing = capsuleInfo.top;

    this.setData({
      topSpacing: topSpacing
    });
  },

  // 微信登录 - 直接调用getUserProfile
  wxLogin: function () {
    console.log('点击了登录按钮，准备获取用户资料');

    // 直接在点击事件中调用getUserProfile
    wx.getUserProfile({
      desc: '用于展示用户信息',
      success: (res) => {
        console.log('真实资料:', res.userInfo);
        const { nickName, avatarUrl } = res.userInfo;

        // 更新页面显示
        this.setData({
          nickName,
          avatarUrl
        });

        wx.showToast({
          title: '获取成功',
          icon: 'success'
        });

        // 登录成功后返回个人中心
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      },
      fail: (err) => {
        console.warn('用户取消授权或失败:', err);

        if (err.errMsg && err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '用户取消授权',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 返回首页
  goHome: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
})