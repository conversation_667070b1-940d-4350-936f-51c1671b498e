# 云开发环境设置指南

## 错误原因
```
Environment not found, there is no default environment exists, please explicitly specify the environment
```

这个错误表示：
- 你还没有开通云开发环境
- 或者没有正确配置云环境ID

## 解决步骤

### 1. 开通云开发环境

1. **打开微信开发者工具**
2. **点击工具栏上的"云开发"按钮**
   - 如果是第一次使用，会看到"开通"按钮
   - 点击"开通"创建云开发环境

3. **选择环境配置**
   - 环境名称：可以自定义，如 "dev" 或 "prod"
   - 环境ID：系统自动生成，格式如 `cloud1-xxxxx`
   - 付费方式：选择按量付费或包年包月

4. **等待环境创建完成**
   - 通常需要几分钟时间
   - 创建完成后会显示环境详情

### 2. 获取环境ID

1. **在云开发控制台中**
2. **查看环境列表**
3. **复制环境ID**（格式如：`cloud1-8gino5ste2508c07`）

### 3. 配置环境ID

将获取到的环境ID填入 `miniprogram/app.js` 文件：

```javascript
wx.cloud.init({
  env: 'cloud1-8gino5ste2508c07', // 替换为你的真实环境ID
  traceUser: true,
});
```

### 4. 部署云函数

1. **右键点击 `cloudfunctions/getOpenid` 文件夹**
2. **选择"上传并部署：云端安装依赖"**
3. **等待部署完成**

### 5. 测试登录功能

配置完成后，重新测试登录功能。

## 常见问题

### Q: 找不到"云开发"按钮？
A: 确保使用的是最新版本的微信开发者工具，并且已经登录了开发者账号。

### Q: 开通失败？
A: 检查小程序是否已经发布，某些功能需要小程序上线后才能开通。

### Q: 环境ID在哪里找？
A: 在云开发控制台的环境列表中，每个环境都有对应的环境ID。

### Q: 可以使用免费额度吗？
A: 是的，云开发提供一定的免费额度，对于开发测试通常足够。

## 完整配置检查

- [ ] 开通云开发环境
- [ ] 获取环境ID
- [ ] 更新 `miniprogram/app.js` 中的环境ID
- [ ] 部署云函数 `getOpenid`
- [ ] 测试登录功能

完成以上步骤后，登录功能应该可以正常工作。