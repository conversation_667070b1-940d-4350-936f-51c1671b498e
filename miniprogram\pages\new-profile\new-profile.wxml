<!--pages/new-profile/new-profile.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🆕 新版头像昵称获取</text>
    <text class="subtitle">微信官方推荐方案</text>
    <view class="dev-tip" wx:if="{{isDevTool}}">
      <text class="tip-text">⚠️ 开发工具环境，头像选择请在真机测试</text>
    </view>
  </view>

  <!-- 用户信息展示区域 -->
  <view class="user-info-section">
    <view class="avatar-section">
      <text class="section-title">选择头像</text>
      <button class="avatar-btn" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image class="avatar" src="{{avatarUrl}}" mode="aspectFill"></image>
        <view class="avatar-tip">点击选择头像</view>
      </button>
    </view>

    <view class="nickname-section">
      <text class="section-title">输入昵称</text>
      <input 
        class="nickname-input" 
        type="nickname" 
        placeholder="请输入昵称" 
        value="{{nickname}}"
        bind:input="onNicknameChange"
        maxlength="20"
      />
    </view>

    <view class="info-status">
      <text class="status-text {{hasUserInfo ? 'complete' : 'incomplete'}}">
        {{hasUserInfo ? '✅ 信息收集完成' : '⏳ 请完善头像和昵称'}}
      </text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button 
      type="primary" 
      class="action-btn {{hasUserInfo ? '' : 'disabled'}}" 
      bindtap="saveUserInfo"
      disabled="{{!hasUserInfo}}"
    >
      💾 保存用户信息
    </button>
    
    <button type="default" class="action-btn" bindtap="resetUserInfo">
      🔄 重置信息
    </button>
    
    <button type="warn" class="action-btn" bindtap="compareOldMethod">
      📊 对比旧版方案
    </button>
  </view>

  <!-- 优势说明 -->
  <view class="advantage-section">
    <text class="advantage-title">🎯 新版方案优势</text>
    <view class="advantage-list">
      <view class="advantage-item">✅ 用户主动选择，体验更好</view>
      <view class="advantage-item">✅ 获取真实头像和昵称</view>
      <view class="advantage-item">✅ 无需弹窗授权</view>
      <view class="advantage-item">✅ 微信官方推荐</view>
      <view class="advantage-item">✅ 不受 is_demote 影响</view>
    </view>
  </view>

  <!-- 调试日志 -->
  <view class="debug-section">
    <view class="debug-header">
      <text class="debug-title">📋 调试日志</text>
      <button class="clear-btn" bindtap="clearLogs">清除</button>
    </view>
    <scroll-view class="debug-logs" scroll-y="true" scroll-top="{{9999}}">
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        {{item}}
      </view>
    </scroll-view>
  </view>
</view>
