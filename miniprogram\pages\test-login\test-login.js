// pages/test-login/test-login.js
Page({
  data: {
    userInfo: null,
    hasUserInfo: false
  },

  onLoad: function (options) {
    console.log('测试页面加载');
  },

  // 测试 getUserProfile
  testGetUserProfile: function() {
    console.log('点击了按钮，准备获取用户资料');
    
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: res => {
        console.log('真实资料:', res.userInfo);
        console.log('昵称:', res.userInfo.nickName);
        console.log('头像:', res.userInfo.avatarUrl);
        
        const { nickName, avatarUrl } = res.userInfo;
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        });
        
        wx.showToast({
          title: '获取成功',
          icon: 'success'
        });
      },
      fail: err => {
        console.warn('用户取消授权或失败:', err);
        wx.showToast({
          title: '获取失败',
          icon: 'none'
        });
      }
    });
  },

  // 测试完整登录流程
  testFullLogin: function() {
    console.log('开始测试完整登录流程...');
    
    const app = getApp();
    
    wx.showLoading({
      title: '登录中...'
    });

    app.wxLogin().then((userInfo) => {
      wx.hideLoading();
      console.log('完整登录成功:', userInfo);
      
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });
    }).catch((error) => {
      wx.hideLoading();
      console.error('完整登录失败:', error);
      
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });
    });
  }
});