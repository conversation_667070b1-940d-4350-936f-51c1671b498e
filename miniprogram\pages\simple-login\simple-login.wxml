<!-- simple-login.wxml -->
<view class="container">
  <view class="title">授权测试页面</view>
  
  <view class="debug-info">{{debugInfo}}</view>
  
  <button type="primary" bindtap="getUserProfile">点击获取头像和昵称</button>
  
  <button type="default" bindtap="testPreview">真机测试说明</button>
  
  <view class="result" wx:if="{{nickName}}">
    <text>昵称：{{nickName}}</text>
    <image src="{{avatarUrl}}" style="width:100rpx;height:100rpx;border-radius:50rpx;"></image>
  </view>
  
  <view class="tips">
    <text>💡 如果没有弹出授权弹窗：</text>
    <text>1. 点击"真机测试说明"查看解决方案</text>
    <text>2. 在真机上测试（推荐）</text>
    <text>3. 检查基础库版本是否 >= 2.10.4</text>
  </view>
</view>