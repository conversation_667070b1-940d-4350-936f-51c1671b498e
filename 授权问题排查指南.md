# 授权问题排查指南

## 问题现象
- 点击按钮没有弹出授权弹窗
- 直接获取到匿名信息（如"微信用户"）
- `wx.getUserProfile` 没有按预期工作

## 排查步骤

### 1. 检查开发工具设置 ⚠️

在微信开发者工具中：

1. **点击右上角"详情"按钮**
2. **检查以下设置**：
   - ✅ 基础库版本：选择 2.27.0 或更高版本
   - ✅ 不校验合法域名：建议勾选（开发阶段）
   - ✅ 不校验 TLS 版本：建议勾选（开发阶段）

3. **本地设置**：
   - ✅ 不校验请求域名、TLS版本及HTTPS证书：勾选
   - ✅ 启用调试：勾选

### 2. 检查小程序AppID ⚠️

1. **确认AppID正确**：
   - 当前AppID：`wx93d681b2a96118bf`
   - 确保这是一个真实的小程序AppID，不是测试号

2. **检查小程序状态**：
   - 小程序必须是已发布状态或开发版本
   - 测试号可能无法正常使用 `wx.getUserProfile`

### 3. 真机测试 🔥 重要

**开发工具限制**：
- 微信开发者工具可能不会弹出真实的授权弹窗
- 可能直接返回模拟数据

**真机测试步骤**：
1. 点击开发工具的"预览"按钮
2. 用微信扫描二维码
3. 在真机上测试授权功能

### 4. 检查代码实现

确认代码完全正确：

```javascript
// ✅ 正确的实现
Page({
  getUserProfile() {
    console.log('点击了按钮'); // 必须有这行日志
    
    wx.getUserProfile({
      desc: '用于完善用户资料', // 必须有desc
      success: res => {
        console.log('获取成功:', res.userInfo);
        // 处理用户信息
      },
      fail: err => {
        console.log('获取失败:', err);
      }
    });
  }
});
```

### 5. 检查小程序配置

在微信公众平台（mp.weixin.qq.com）：

1. **登录小程序管理后台**
2. **检查开发设置**：
   - 确认AppID正确
   - 检查服务器域名配置
3. **检查版本管理**：
   - 确保有开发版本或体验版本

## 解决方案

### 方案1：真机测试（推荐）
```bash
1. 点击开发工具"预览"
2. 微信扫码
3. 在手机上测试
```

### 方案2：检查开发工具版本
```bash
1. 更新到最新版微信开发者工具
2. 重启开发工具
3. 重新编译项目
```

### 方案3：使用体验版测试
```bash
1. 在开发工具中点击"上传"
2. 在小程序后台设置为体验版
3. 用体验版测试授权功能
```

### 方案4：检查网络环境
```bash
1. 确保网络连接正常
2. 尝试切换网络环境
3. 检查防火墙设置
```

## 常见问题

### Q: 开发工具中不弹窗？
A: 这是正常的，开发工具可能不会弹出真实授权弹窗，请在真机上测试。

### Q: 真机上也不弹窗？
A: 检查小程序是否已发布，AppID是否正确，基础库版本是否足够。

### Q: 提示"该小程序未发布"？
A: 需要先发布小程序或使用体验版进行测试。

### Q: 一直获取匿名信息？
A: 可能是使用了测试AppID，需要使用真实的小程序AppID。

## 最终验证

如果以上都检查过了，请：

1. **在真机上测试**
2. **查看真机上的控制台日志**
3. **确认是否弹出授权弹窗**
4. **检查获取到的用户信息是否真实**

记住：**开发工具的表现可能与真机不同，真机测试是最准确的！**