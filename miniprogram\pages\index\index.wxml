<!--index.wxml-->
<view class="page-container">
  <!-- Top Stats -->
  <view class="top-stats">
    <view class="stats-left">
      <view class="stats-item">
        <view class="stats-label">陪伴天数</view>
        <view class="stats-value">{{petStats.daysWithPet}}</view>
      </view>
      <view class="stats-item">
        <view class="stats-label">总经验</view>
        <view class="stats-value">{{petStats.totalExp}}</view>
      </view>
    </view>
  </view>

  <!-- Pet Display -->
  <view class="pet-section">
    <view class="pet-card">
      <view class="pet-card-content">
        <!-- 左侧：宠物状态 -->
        <view class="pet-stats-left">
          <view class="stats-title">宠物状态</view>
          
          <view class="stat-item">
            <view class="stat-header">
              <text class="stat-icon">❤️</text>
              <text class="stat-label">健康值</text>
              <text class="stat-value">{{petStats.health}}</text>
            </view>
            <view class="stat-bar">
              <view class="stat-fill health-fill" style="width: {{petStats.health}}%"></view>
            </view>
          </view>
          
          <view class="stat-item">
            <view class="stat-header">
              <text class="stat-icon">⚡</text>
              <text class="stat-label">活力值</text>
              <text class="stat-value">{{petStats.energy}}</text>
            </view>
            <view class="stat-bar">
              <view class="stat-fill energy-fill" style="width: {{petStats.energy}}%"></view>
            </view>
          </view>
          
          <view class="stat-item">
            <view class="stat-header">
              <text class="stat-icon">⭐</text>
              <text class="stat-label">开心值</text>
              <text class="stat-value">{{petStats.happiness}}</text>
            </view>
            <view class="stat-bar">
              <view class="stat-fill happiness-fill" style="width: {{petStats.happiness}}%"></view>
            </view>
          </view>
          
          <view class="stat-item stat-item-last">
            <view class="stat-header">
              <text class="stat-icon">🌱</text>
              <text class="stat-label">成长值</text>
              <text class="stat-value">{{petStats.growth || 78}}</text>
            </view>
            <view class="stat-bar">
              <view class="stat-fill growth-fill" style="width: {{petStats.growth || 78}}%"></view>
            </view>
          </view>
        </view>
        
        <!-- 右侧：宠物头像和信息 -->
        <view class="pet-info-right">
          <view class="pet-avatar">
            <view class="pet-avatar-bg"></view>
            <view class="pet-avatar-inner">
              <text class="pet-emoji {{petAnimation === 'feed' ? 'pet-scale' : petAnimation === 'play' ? 'pet-bounce' : petAnimation === 'walk' ? 'pet-move' : ''}}">🦊</text>
            </view>
            <!-- 浮动经验提示 -->
            <view wx:if="{{showFloatingExp}}" class="floating-exp">+25 EXP!</view>
          </view>
          
          <view class="pet-name-row">
            <view class="pet-name">小橙</view>
            <view class="chat-icon" bindtap="goToChat">
              <text class="chat-emoji">💬</text>
            </view>
          </view>
          
          <view class="pet-status">今天状态超棒！</view>
        </view>
      </view>
      
      <!-- Experience Bar with Level -->
      <view class="exp-container">
        <view class="exp-level">Lv.{{petStats.level}}</view>
        <view class="exp-bar-container">
          <view class="exp-bar">
            <view class="exp-fill" style="width: {{(petStats.experience / petStats.maxExp) * 100}}%"></view>
          </view>
          <view class="exp-text">{{petStats.experience}}/{{petStats.maxExp}} EXP</view>
        </view>
      </view>
    </view>
  </view>



  <!-- Action Buttons -->
  <view class="action-section">
    <view class="action-buttons">
      <view class="action-button feed-button" bindtap="handlePetInteraction" data-action="feed">
        <text class="action-icon">🍖</text>
        <text class="action-label">喂食</text>
      </view>
      <view class="action-button play-button" bindtap="handlePetInteraction" data-action="play">
        <text class="action-icon">❤️</text>
        <text class="action-label">互动</text>
      </view>
      <view class="action-button walk-button" bindtap="handlePetInteraction" data-action="walk">
        <text class="action-icon">👣</text>
        <text class="action-label">散步</text>
      </view>
      <view class="action-button game-button" bindtap="handlePetInteraction" data-action="game">
        <text class="action-icon">🎮</text>
        <text class="action-label">游戏</text>
      </view>
    </view>
  </view>

  <!-- Tab Navigation -->
  <view class="tab-section">
    <view class="tab-container">
      <view class="tab-grid">
        <view class="tab-item {{activeTab === 'overview' ? 'tab-active' : ''}}" 
              bindtap="switchTab" data-tab="overview">
          <text class="tab-icon">📅</text>
          <text class="tab-label">概览</text>
        </view>
        <view class="tab-item {{activeTab === 'tasks' ? 'tab-active' : ''}}" 
              bindtap="switchTab" data-tab="tasks">
          <text class="tab-icon">🏆</text>
          <text class="tab-label">任务</text>
        </view>
        <view class="tab-item {{activeTab === 'achievements' ? 'tab-active' : ''}}" 
              bindtap="switchTab" data-tab="achievements">
          <text class="tab-icon">🎁</text>
          <text class="tab-label">成就</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Content Area -->
  <view class="content-section">
    <!-- Tasks Tab -->
    <view wx:if="{{activeTab === 'tasks'}}" class="content-panel">
      <view class="panel-card">
        <view class="panel-header">
          <view class="panel-title">今日任务</view>
          <view class="panel-subtitle">{{completedTasksCount}}/{{tasks.length}}</view>
        </view>
        
        <view class="task-list">
          <view wx:for="{{tasks}}" wx:key="id" class="task-item">
            <view class="task-icon {{item.color}}">
              <text>{{item.icon}}</text>
            </view>
            <view class="task-content">
              <view class="task-name">{{item.name}}</view>
              <view class="task-desc">{{item.description}}</view>
            </view>
            <view class="task-right">
              <view class="task-reward">+{{item.reward}} EXP</view>
              <view class="task-status {{item.completed ? 'task-completed' : 'task-pending'}}"
                    bindtap="completeTask" data-id="{{item.id}}">
                <text wx:if="{{item.completed}}">✓</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Achievements Tab -->
    <view wx:if="{{activeTab === 'achievements'}}" class="content-panel">
      <view class="panel-card">
        <view class="panel-header">
          <view class="panel-title">成就进度</view>
        </view>
        
        <view class="achievement-list">
          <view wx:for="{{achievements}}" wx:key="name" class="achievement-item">
            <view class="achievement-content">
              <view class="achievement-name">{{item.name}}</view>
              <view class="achievement-progress">{{item.progress}}%</view>
            </view>
            <view class="achievement-bar">
              <view class="achievement-fill" style="width: {{item.progress}}%"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Overview Tab -->
    <view wx:if="{{activeTab === 'overview'}}" class="content-panel">
      <view class="panel-card">
        <view class="panel-header">
          <view class="panel-title">今日摘要</view>
        </view>
        
        <view class="overview-grid">
          <view class="overview-item">
            <view class="overview-value">12</view>
            <view class="overview-label">已完成任务</view>
          </view>
          <view class="overview-item">
            <view class="overview-value">340</view>
            <view class="overview-label">今日经验</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>