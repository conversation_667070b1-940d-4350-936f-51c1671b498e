/* pages/login/login.wxss */
page {
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
}

/* Header */
.header {
  padding: 8rpx 40rpx 0;
  height: 88rpx;
  display: flex;
  align-items: flex-start;
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
}

/* Login Content */
.login-content {
  padding: 100rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 60rpx;
}

/* User Display */
.user-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.avatar {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-placeholder {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
}

.nickname-placeholder {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* Login Button */
button[type="primary"] {
  width: 500rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
}

button[type="primary"]::after {
  border: none;
}

/* Skip Login */
.skip-login {
  padding: 20rpx;
}

.skip-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: underline;
}