// pages/new-profile/new-profile.js
Page({
  data: {
    avatarUrl: '/images/default-avatar.png',
    nickname: '',
    hasUserInfo: false,
    logs: []
  },

  onLoad() {
    this.addLog('=== 新版头像昵称获取页面 ===');
    this.addLog('使用微信推荐的新版方案');
    this.addLog('用户主动选择头像和填写昵称');
  },

  // 添加日志
  addLog(message) {
    const now = new Date();
    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    const logMessage = `[${timeStr}] ${message}`;
    
    console.log(logMessage);
    
    this.setData({
      logs: [...this.data.logs, logMessage]
    });
  },

  // 选择头像
  onChooseAvatar(e) {
    this.addLog('用户点击了选择头像');
    const { avatarUrl } = e.detail;
    
    if (avatarUrl) {
      this.addLog(`✅ 获取到头像: ${avatarUrl}`);
      this.setData({
        avatarUrl: avatarUrl
      });
      
      this.checkUserInfo();
    } else {
      this.addLog('❌ 未获取到头像');
    }
  },

  // 昵称输入
  onNicknameChange(e) {
    const nickname = e.detail.value;
    this.addLog(`用户输入昵称: ${nickname}`);
    
    this.setData({
      nickname: nickname
    });
    
    this.checkUserInfo();
  },

  // 检查用户信息完整性
  checkUserInfo() {
    const { avatarUrl, nickname } = this.data;
    const hasAvatar = avatarUrl && !avatarUrl.includes('default-avatar');
    const hasNickname = nickname && nickname.trim().length > 0;
    
    if (hasAvatar && hasNickname) {
      this.addLog('✅ 用户信息收集完成');
      this.setData({
        hasUserInfo: true
      });
    } else {
      this.setData({
        hasUserInfo: false
      });
    }
  },

  // 保存用户信息
  saveUserInfo() {
    const { avatarUrl, nickname } = this.data;
    
    this.addLog('=== 开始保存用户信息 ===');
    this.addLog(`头像: ${avatarUrl}`);
    this.addLog(`昵称: ${nickname}`);
    
    // 构造用户信息对象
    const userInfo = {
      nickName: nickname,
      avatarUrl: avatarUrl,
      createTime: new Date().toISOString(),
      source: 'new-profile-component'
    };
    
    try {
      // 保存到本地存储
      wx.setStorageSync('userInfo', userInfo);
      this.addLog('✅ 用户信息已保存到本地');
      
      // 保存到全局数据
      const app = getApp();
      if (app) {
        app.globalData.userInfo = userInfo;
        app.globalData.isLoggedIn = true;
        this.addLog('✅ 用户信息已保存到全局');
      }
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 可以在这里调用云函数保存到数据库
      this.saveToCloud(userInfo);
      
    } catch (error) {
      this.addLog(`❌ 保存失败: ${error.message}`);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  // 保存到云数据库
  async saveToCloud(userInfo) {
    try {
      this.addLog('开始保存到云数据库...');
      
      // 先获取 openid
      const loginRes = await this.promisify(wx.login)();
      this.addLog(`获取到 code: ${loginRes.code}`);
      
      // 调用云函数获取 openid
      const cloudRes = await wx.cloud.callFunction({
        name: 'getOpenid',
        env: 'cloud1-8gino5ste2508c07',
        data: { code: loginRes.code }
      });
      
      const openid = cloudRes.result.openid;
      this.addLog(`获取到 openid: ${openid}`);
      
      // 保存到数据库
      const db = wx.cloud.database({
        env: 'cloud1-8gino5ste2508c07'
      });
      
      const saveData = {
        ...userInfo,
        openid: openid,
        updateTime: new Date()
      };
      
      await db.collection('users').add({
        data: saveData
      });
      
      this.addLog('✅ 用户信息已保存到云数据库');
      
    } catch (error) {
      this.addLog(`⚠️ 云数据库保存失败: ${error.message}`);
      this.addLog('本地保存仍然有效');
    }
  },

  // Promise 化微信 API
  promisify(fn) {
    return (options = {}) => {
      return new Promise((resolve, reject) => {
        fn({
          ...options,
          success: resolve,
          fail: reject
        });
      });
    };
  },

  // 清除日志
  clearLogs() {
    this.setData({
      logs: []
    });
  },

  // 重置用户信息
  resetUserInfo() {
    this.setData({
      avatarUrl: '/images/default-avatar.png',
      nickname: '',
      hasUserInfo: false
    });
    this.addLog('用户信息已重置');
  },

  // 对比旧版方案
  compareOldMethod() {
    wx.showModal({
      title: '新旧方案对比',
      content: `旧版方案 (getUserProfile)：
• 弹出授权弹窗
• 可能返回模拟数据 (is_demote: true)
• 用户体验较差
• 接口已调整

新版方案 (头像昵称组件)：
• 用户主动选择
• 获取真实信息
• 体验更好
• 微信官方推荐`,
      showCancel: false,
      confirmText: '知道了'
    });
  }
});
