<!--pages/health/index.wxml-->
<view class="health-page">
  <!-- 页面头部 -->
  <view class="page-header" style="padding-top: {{topSpacing}}px;">
    <view class="header-title">全部任务</view>
    <view class="header-subtitle">完成任务，让宠物更健康</view>
  </view>

  <!-- 今日进度概览 -->
  <view class="progress-overview">
    <view class="overview-card">
      <view class="overview-stats">
        <view class="stat-item">
          <text class="stat-number">{{completedTasks}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{totalTasks}}</text>
          <text class="stat-label">总任务</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{todayExp}}</text>
          <text class="stat-label">今日经验</text>
        </view>
      </view>
      
      <!-- 进度条 -->
      <view class="progress-bar-container">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercentage}}%"></view>
        </view>
        <text class="progress-text">{{Math.round(progressPercentage)}}% 完成</text>
      </view>
    </view>
  </view>

  <!-- 任务分类标签 -->
  <view class="task-categories">
    <scroll-view scroll-x="true" class="category-scroll">
      <view class="category-list">
        <view 
          class="category-item {{currentCategory === item.id ? 'active' : ''}}"
          wx:for="{{taskCategories}}" 
          wx:key="id"
          data-category="{{item.id}}"
          bindtap="switchCategory"
        >
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-name">{{item.name}}</text>
          <view class="category-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 任务列表 -->
  <view class="task-list-section">
    <task-list 
      tasks="{{filteredTasks}}"
      show-progress="{{true}}"
      allow-toggle="{{true}}"
      bind:taskComplete="onTaskComplete"
    ></task-list>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredTasks.length === 0}}">
    <image src="/images/icons/empty-task.png" class="empty-icon"></image>
    <text class="empty-title">暂无任务</text>
    <text class="empty-desc">所有任务都已完成，真棒！</text>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab-button" bindtap="showAddTaskModal">
    <text class="fab-icon">+</text>
  </view>
</view>