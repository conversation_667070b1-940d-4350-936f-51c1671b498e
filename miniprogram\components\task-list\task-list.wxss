/* components/task-list/task-list.wxss */
.task-list-container {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin: 20rpx 0;
}

/* 任务头部 */
.task-header {
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d5a27;
}

.progress-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #5a8a52;
}

/* 总进度条 */
.overall-progress {
  position: relative;
  height: 12rpx;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #e0e0e0;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
  transition: width 0.8s ease;
}

/* 任务列表 */
.task-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 任务项 */
.task-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-item:active {
  transform: scale(0.98);
}

.task-item.completed {
  background: #f1f8e9;
  opacity: 0.8;
}

/* 任务状态 */
.task-status {
  margin-right: 20rpx;
}

.status-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.status-dot.pending {
  background: #e0e0e0;
  border: 3rpx solid #bdbdbd;
}

.status-dot.completed {
  background: #4CAF50;
  border: 3rpx solid #4CAF50;
}

.check-icon {
  width: 20rpx;
  height: 20rpx;
}

/* 任务内容 */
.task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.task-main {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.task-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.task-info {
  flex: 1;
}

.task-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.task-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 任务进度条 */
.task-progress {
  position: relative;
  height: 6rpx;
  border-radius: 3rpx;
  overflow: hidden;
  margin-top: 8rpx;
}

.mini-progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #e0e0e0;
}

.mini-progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #4CAF50;
  transition: width 0.5s ease;
}

/* 奖励信息 */
.task-reward {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20rpx;
}

.reward-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #FF9800;
}

.reward-unit {
  font-size: 20rpx;
  color: #999;
}

/* 完成特效 */
.complete-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.effect-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(76, 175, 80, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: complete-ripple 0.6s ease-out;
}

/* 奖励动画 */
.reward-animation {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.reward-animation.show {
  opacity: 1;
  animation: reward-float 2s ease-out;
}

.reward-animation-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF9800;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 动画效果 */
@keyframes complete-ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200rpx;
    height: 200rpx;
    opacity: 0;
  }
}

@keyframes reward-float {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  20% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  80% {
    transform: translate(-50%, -60%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -80%) scale(0.8);
    opacity: 0;
  }
}

/* 任务完成动画 */
.task-completing {
  animation: task-completing 0.3s ease;
}

.task-completed {
  animation: task-completed 0.3s ease;
}

@keyframes task-completing {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes task-completed {
  0% {
    background: white;
  }
  100% {
    background: #f1f8e9;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .task-item {
    padding: 20rpx;
  }
  
  .task-name {
    font-size: 26rpx;
  }
  
  .task-desc {
    font-size: 22rpx;
  }
  
  .status-dot {
    width: 36rpx;
    height: 36rpx;
  }
  
  .task-icon {
    width: 32rpx;
    height: 32rpx;
  }
}