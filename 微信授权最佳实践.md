# 微信授权最佳实践

## 重要规则

### 1. 必须用户主动点击
- ✅ 获取头像行为必须绑定在明确的用户点击行为上
- ❌ 不能自动触发授权
- ❌ 不能在页面加载时自动调用

### 2. 标准按钮组件
```xml
<!-- 正确的按钮写法 -->
<button type="primary" bindtap="getUserProfile">获取头像和昵称</button>
```

### 3. 直接调用方式
```javascript
// 正确：在点击事件中直接调用
getUserProfile: function() {
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: res => {
      console.log('用户信息:', res.userInfo);
      // 处理用户信息...
    },
    fail: err => {
      console.warn('用户拒绝授权:', err);
    }
  });
}
```

## 当前实现 ✅

### 1. 个人设置页面
```xml
<button type="primary" bindtap="wxLogin">获取头像和昵称</button>
```

### 2. 登录页面
```xml
<button type="primary" bindtap="wxLogin">获取头像和昵称</button>
```

### 3. 测试页面
```xml
<button type="primary" bindtap="testGetUserProfile">测试 getUserProfile</button>
```

## 授权流程

### 1. 用户点击按钮
- 用户主动点击"获取头像和昵称"按钮

### 2. 弹出授权弹窗
- 微信自动弹出授权确认弹窗
- 显示授权描述："用于完善用户资料"

### 3. 用户确认授权
- 用户点击"允许"或"拒绝"

### 4. 处理授权结果
- 成功：获取真实的用户头像和昵称
- 失败：处理用户拒绝的情况

## 注意事项

### 1. 按钮文案
- ✅ "获取头像和昵称" - 明确说明功能
- ❌ "登录" - 可能被认为是诱导授权
- ❌ "一键登录" - 可能被认为是诱导授权

### 2. 授权描述
```javascript
wx.getUserProfile({
  desc: '用于完善用户资料', // 必填，会显示给用户
  // ...
});
```

### 3. 错误处理
```javascript
fail: err => {
  if (err.errMsg.includes('cancel')) {
    // 用户取消授权
  } else if (err.errMsg.includes('deny')) {
    // 用户拒绝授权
  }
}
```

## 测试建议

### 1. 开发工具测试
- 可能显示"微信用户"（模拟数据）
- 主要测试流程是否正常

### 2. 真机测试
- 扫码在真机上测试
- 能获取到真实的用户信息

### 3. 审核要求
- 不能诱导用户授权
- 按钮文案要明确
- 必须用户主动点击

## 常见问题

### Q: 为什么还是显示"微信用户"？
A: 可能是开发工具限制，请在真机上测试。

### Q: 授权弹窗不出现？
A: 检查是否在用户点击事件中直接调用，不能在异步回调中调用。

### Q: 审核被拒？
A: 检查按钮文案是否明确，是否存在诱导授权行为。

现在的实现完全符合微信的授权规范！