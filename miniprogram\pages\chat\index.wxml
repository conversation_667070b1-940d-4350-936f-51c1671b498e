<!--chat.wxml-->
<view class="page-container">
  <!-- 聊天头部 -->
  <view class="chat-header" style="padding-top: {{topSpacing}}px;">
    <view class="header-content">
      <view class="back-button" bindtap="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="pet-avatar-small">
        <text class="pet-emoji-small">🦊</text>
      </view>
      <view class="pet-info">
        <view class="pet-name">小橙</view>
        <view class="pet-status">在线</view>
      </view>
    </view>
  </view>

  <!-- 聊天消息区域 -->
  <scroll-view class="chat-messages" scroll-y="true" scroll-top="{{scrollTop}}" scroll-into-view="{{toView}}">
    <view wx:for="{{messages}}" wx:key="id" class="message-item {{item.type === 'user' ? 'message-user' : 'message-pet'}}">
      <view wx:if="{{item.type === 'pet'}}" class="message-avatar">
        <text class="avatar-emoji">🦊</text>
      </view>
      <view class="message-bubble {{item.type === 'user' ? 'bubble-user' : 'bubble-pet'}}">
        <text class="message-text">{{item.content}}</text>
        <view class="message-time">{{item.time}}</view>
      </view>
      <view wx:if="{{item.type === 'user'}}" class="message-avatar">
        <text class="avatar-emoji">👤</text>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="chat-input-area">
    <view class="input-container">
      <input class="message-input" 
             placeholder="和小橙聊聊吧..." 
             value="{{inputText}}" 
             bindinput="onInputChange"
             confirm-type="send"
             bindconfirm="sendMessage" />
      <view class="send-button {{inputText ? 'send-active' : ''}}" bindtap="sendMessage">
        <text class="send-icon">📤</text>
      </view>
    </view>
  </view>
</view>