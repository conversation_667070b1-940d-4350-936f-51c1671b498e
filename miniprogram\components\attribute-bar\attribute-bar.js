// components/attribute-bar/attribute-bar.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    label: {
      type: String,
      value: '属性'
    },
    current: {
      type: Number,
      value: 0,
      observer: function(newVal, oldVal) {
        this.updateProgress(newVal, oldVal);
      }
    },
    max: {
      type: Number,
      value: 100
    },
    color: {
      type: String,
      value: '#4CAF50'
    },
    icon: {
      type: String,
      value: ''
    },
    showAnimation: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    percentage: 0,
    displayCurrent: 0,
    barColor: '#4CAF50',
    textColor: '#333',
    animationClass: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新进度条
     */
    updateProgress(newVal, oldVal) {
      const { max } = this.data;
      const percentage = Math.min(100, Math.max(0, (newVal / max) * 100));
      
      // 根据数值确定颜色
      const barColor = this.getColorByValue(percentage);
      
      if (this.data.showAnimation && oldVal !== undefined) {
        // 动画更新数值
        this.animateValue(oldVal, newVal);
      } else {
        // 直接更新
        this.setData({
          percentage: percentage,
          displayCurrent: newVal,
          barColor: barColor
        });
      }
    },

    /**
     * 根据数值获取颜色
     */
    getColorByValue(percentage) {
      if (this.data.color !== '#4CAF50') {
        // 如果指定了自定义颜色，使用自定义颜色
        return this.data.color;
      }
      
      // 根据百分比动态调整颜色
      if (percentage >= 80) {
        return '#4CAF50'; // 绿色 - 良好
      } else if (percentage >= 60) {
        return '#8BC34A'; // 浅绿色 - 正常
      } else if (percentage >= 40) {
        return '#FFC107'; // 黄色 - 注意
      } else if (percentage >= 20) {
        return '#FF9800'; // 橙色 - 警告
      } else {
        return '#F44336'; // 红色 - 危险
      }
    },

    /**
     * 动画更新数值
     */
    animateValue(fromVal, toVal) {
      const duration = 1000; // 动画持续时间
      const steps = 30; // 动画步数
      const stepTime = duration / steps;
      const stepValue = (toVal - fromVal) / steps;
      
      let currentStep = 0;
      const timer = setInterval(() => {
        currentStep++;
        const currentValue = fromVal + (stepValue * currentStep);
        const percentage = Math.min(100, Math.max(0, (currentValue / this.data.max) * 100));
        
        this.setData({
          displayCurrent: Math.round(currentValue),
          percentage: percentage,
          barColor: this.getColorByValue(percentage)
        });
        
        if (currentStep >= steps) {
          clearInterval(timer);
          // 确保最终值准确
          this.setData({
            displayCurrent: toVal,
            percentage: Math.min(100, Math.max(0, (toVal / this.data.max) * 100))
          });
        }
      }, stepTime);
    },

    /**
     * 格式化显示文本
     */
    formatDisplayText() {
      const { displayCurrent, max } = this.data;
      return `${displayCurrent}/${max}`;
    },

    /**
     * 属性条点击事件
     */
    onAttributeBarTap() {
      // 播放点击动画
      this.setData({
        animationClass: 'bar-tap'
      });
      
      // 触发自定义事件
      this.triggerEvent('attributeTap', {
        label: this.data.label,
        current: this.data.current,
        max: this.data.max,
        percentage: this.data.percentage
      });
      
      // 清除动画类
      setTimeout(() => {
        this.setData({
          animationClass: ''
        });
      }, 300);
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'current, max': function(current, max) {
      if (max > 0) {
        const percentage = Math.min(100, Math.max(0, (current / max) * 100));
        this.setData({
          percentage: percentage,
          displayCurrent: current,
          barColor: this.getColorByValue(percentage)
        });
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化时设置数据
      this.updateProgress(this.data.current);
    }
  }
});