/* index.wxss */
page {
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed; /* 固定背景，防止滚动时断节 */
  min-height: 100vh;
}

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  padding-bottom: 40rpx; /* 减少底部间距 */
  transition: all 0.3s ease;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
  .page-container {
    padding-left: 20rpx;
    padding-right: 20rpx;
  }
  
  .pet-section {
    padding: 0rpx 20rpx 20rpx;
  }
  
  .action-section {
    padding: 10rpx 20rpx 20rpx;
  }
  
  .tab-section {
    padding: 10rpx 20rpx 20rpx;
  }
  
  .content-section {
    padding: 10rpx 20rpx 20rpx;
  }
  
  .stats-left {
    gap: 20rpx;
  }
  
  .stats-item {
    padding: 6rpx 12rpx;
  }
  
  .stats-label {
    font-size: 22rpx;
  }
  
  .stats-value {
    font-size: 26rpx;
  }
  
  .action-button {
    width: 100rpx;
    height: 100rpx;
  }
  
  .action-icon {
    font-size: 36rpx;
  }
  
  .action-label {
    font-size: 18rpx;
  }
}

@media screen and (min-width: 768px) {
  .page-container {
    max-width: 750rpx;
    margin: 0 auto;
  }
  
  .pet-card-content {
    gap: 40rpx;
  }
  
  .action-buttons {
    gap: 30rpx;
  }
  
  .action-button {
    width: 140rpx;
    height: 140rpx;
  }
  
  .action-icon {
    font-size: 44rpx;
  }
  
  .action-label {
    font-size: 22rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  page {
    background: linear-gradient(135deg, #1e1b4b 0%, #581c87 50%, #831843 100%);
  }
  
  .page-container {
    background: linear-gradient(135deg, #1e1b4b 0%, #581c87 50%, #831843 100%);
  }
  
  .stats-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1rpx solid rgba(255, 255, 255, 0.15);
  }
  
  .pet-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1rpx solid rgba(255, 255, 255, 0.15);
  }
  
  .panel-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1rpx solid rgba(255, 255, 255, 0.15);
  }
  
  .task-item, .achievement-item, .overview-item {
    background: rgba(255, 255, 255, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
  }
}

/* Top Stats */
.top-stats {
  padding: 100rpx 40rpx 20rpx;
  display: flex;
  align-items: center;
}

.stats-left {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 25rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: slideInDown 0.6s ease-out;
}

.stats-item:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-item:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.3);
}

.stats-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.stats-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #fbbf24;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* Pet Section */
.pet-section {
  padding: 20rpx 40rpx 20rpx;
}

.pet-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 60rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInUp 0.8s ease-out;
}

.pet-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.7);
}

.pet-card-content {
  display: flex;
  gap: 30rpx;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.pet-stats-left {
  flex: 0 0 40%;
  min-width: 0;
}

.pet-info-right {
  flex: 0 0 60%;
  text-align: center;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.pet-avatar {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto 30rpx;
}

.pet-avatar-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #fb923c, #ec4899);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.pet-avatar-inner {
  position: absolute;
  inset: 10rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-emoji {
  font-size: 90rpx;
  animation: bounce 2s infinite;
}

/* 浮动经验提示 */
.floating-exp {
  position: absolute;
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  color: #fbbf24;
  font-size: 28rpx;
  font-weight: bold;
  animation: floatUp 2s ease-out;
}

@keyframes floatUp {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-40rpx);
  }
}

.pet-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.pet-status {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 左侧宠物状态样式 */
.stats-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.stat-item {
  margin-bottom: 20rpx;
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.stat-icon {
  font-size: 24rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  margin-left: 8rpx;
}

.stat-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.stat-bar {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
  height: 12rpx;
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.health-fill {
  background: linear-gradient(90deg, #ef4444, #f87171);
}

.energy-fill {
  background: linear-gradient(90deg, #eab308, #facc15);
}

.happiness-fill {
  background: linear-gradient(90deg, #a855f7, #c084fc);
}

.growth-fill {
  background: linear-gradient(90deg, #10b981, #34d399);
}

/* 最后一个状态项与右侧pet-status对齐 */
.stat-item-last {
  margin-bottom: 0;
}

/* 右侧宠物信息样式 */
.pet-name-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  margin-bottom: 10rpx;
}

.chat-icon {
  width: 50rpx;
  height: 50rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.chat-icon:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(76, 175, 80, 0.4);
}

.chat-emoji {
  font-size: 24rpx;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* Experience Container */
.exp-container {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
  margin-top: 10rpx;
}

.exp-level {
  font-size: 28rpx;
  font-weight: bold;
  color: #fbbf24;
  min-width: 70rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0; /* 与经验条顶部对齐 */
}

.exp-bar-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.exp-bar {
  background: #e0e0e0;
  border-radius: 12rpx;
  height: 24rpx;
  margin-bottom: 6rpx;
  overflow: hidden;
}

.exp-fill {
  height: 100%;
  background: linear-gradient(90deg, #9c27b0, #e91e63);
  border-radius: 12rpx;
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.exp-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

.exp-text {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
}



/* Action Buttons */
.action-section {
  padding: 20rpx 40rpx 20rpx;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.action-button:hover::before {
  width: 100%;
  height: 100%;
}

.action-button:active {
  transform: scale(0.95);
}

.action-button:hover {
  transform: translateY(-4rpx) scale(1.05);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.feed-button {
  background: rgba(239, 68, 68, 0.1);
  border: 2rpx solid rgba(239, 68, 68, 0.3);
}

.play-button {
  background: rgba(34, 197, 94, 0.1);
  border: 2rpx solid rgba(34, 197, 94, 0.3);
}

.walk-button {
  background: rgba(59, 130, 246, 0.1);
  border: 2rpx solid rgba(59, 130, 246, 0.3);
}

.game-button {
  background: rgba(139, 92, 246, 0.1);
  border: 2rpx solid rgba(139, 92, 246, 0.3);
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.action-label {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}

/* Tab Section */
.tab-section {
  padding: 20rpx 40rpx 20rpx;
}

.tab-container {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 40rpx;
  padding: 10rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.tab-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10rpx;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 30rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.tab-item:hover::before {
  left: 100%;
}

.tab-active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.4);
  transform: translateY(-2rpx);
}

.tab-active .tab-icon {
  animation: bounce 0.6s ease;
}

.tab-icon {
  font-size: 30rpx;
  margin-bottom: 5rpx;
}

.tab-label {
  font-size: 24rpx;
  font-weight: 500;
}

/* Content Section */
.content-section {
  padding: 20rpx 40rpx 20rpx;
}

.content-panel {
  margin-bottom: 20rpx; /* 减少面板间距 */
}

.panel-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 40rpx;
  padding: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: scaleIn 0.5s ease-out;
  transition: all 0.3s ease;
}

.panel-card:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx; /* 减少标题下方间距 */
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.panel-subtitle {
  font-size: 24rpx;
  color: #666;
}

/* Task List */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx; /* 减少任务项间距 */
}

.task-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  animation: slideInLeft 0.6s ease-out;
  transition: all 0.3s ease;
}

.task-item:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: translateX(8rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-item:nth-child(2) {
  animation-delay: 0.1s;
}

.task-item:nth-child(3) {
  animation-delay: 0.2s;
}

.task-item:nth-child(4) {
  animation-delay: 0.3s;
}

.task-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 30rpx;
}

.bg-yellow {
  background: #eab308;
  color: white;
}

.bg-blue {
  background: #3b82f6;
  color: white;
}

.bg-green {
  background: #10b981;
  color: white;
}

.bg-purple {
  background: #8b5cf6;
  color: white;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.task-desc {
  font-size: 24rpx;
  color: #666;
}

.task-right {
  text-align: right;
}

.task-reward {
  font-size: 24rpx;
  font-weight: bold;
  color: #ff9800;
  margin-bottom: 10rpx;
}

.task-status {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-status::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.task-status:active::before {
  width: 100%;
  height: 100%;
}

.task-completed {
  background: #4caf50;
  animation: pulse 2s infinite;
}

.task-pending {
  background: #ccc;
}

.task-pending:hover {
  background: #4caf50;
  transform: scale(1.1);
}

/* Achievement List */
.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx; /* 减少成就项间距 */
}

.achievement-item {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  animation: slideInRight 0.6s ease-out;
  transition: all 0.3s ease;
}

.achievement-item:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: translateX(-8rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.achievement-item:nth-child(2) {
  animation-delay: 0.1s;
}

.achievement-item:nth-child(3) {
  animation-delay: 0.2s;
}

.achievement-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx; /* 减少成就内容间距 */
}

.achievement-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.achievement-progress {
  font-size: 24rpx;
  color: #666;
}

.achievement-bar {
  background: #e0e0e0;
  border-radius: 10rpx;
  height: 8rpx;
  overflow: hidden;
}

.achievement-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #3b82f6);
  border-radius: 10rpx;
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.achievement-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* Overview Grid */
.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx; /* 减少概览项间距 */
}

.overview-item {
  text-align: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 30rpx;
  animation: scaleIn 0.6s ease-out;
  transition: all 0.3s ease;
}

.overview-item:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.overview-item:nth-child(2) {
  animation-delay: 0.2s;
}

.overview-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.overview-item:first-child .overview-value {
  color: #10b981;
}

.overview-item:last-child .overview-value {
  color: #8b5cf6;
}

.overview-label {
  font-size: 24rpx;
  color: #666;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

/* 新增动画效果 */
@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-40rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Pet Animation Effects */
.pet-scale {
  animation: petScale 0.5s ease-in-out;
}

.pet-bounce {
  animation: petBounce 1s ease-in-out;
}

.pet-move {
  animation: petMove 1s ease-in-out;
}

@keyframes petScale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes petBounce {
  0%, 100% {
    transform: translateY(0);
  }
  25%, 75% {
    transform: translateY(-20rpx);
  }
  50% {
    transform: translateY(-30rpx);
  }
}

@keyframes petMove {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(20rpx);
  }
  75% {
    transform: translateX(-20rpx);
  }
}
