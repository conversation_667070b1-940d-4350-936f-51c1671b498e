/* pages/login/login.wxss */
page {
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
}

/* Header */
.header {
  padding: 8rpx 40rpx 0;
  height: 88rpx;
  display: flex;
  align-items: flex-start;
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
}

/* Login Content */
.login-content {
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  gap: 50rpx;
}

/* 头像选择区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
}

.avatar-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.avatar-btn:active .avatar {
  transform: scale(0.95);
  border-color: rgba(255, 255, 255, 0.8);
}

.avatar-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 昵称输入区域 */
.nickname-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
  margin-top: 20rpx;
}

.nickname-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 完成按钮 */
.complete-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.complete-btn.disabled {
  opacity: 0.5;
}

.complete-btn:active {
  transform: scale(0.98);
}

button[type="primary"]::after {
  border: none;
}

/* Skip Login */
.skip-login {
  text-align: center;
  padding: 20rpx;
}

.skip-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: underline;
}

/* 说明文字 */
.login-tips {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.tip-item {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}