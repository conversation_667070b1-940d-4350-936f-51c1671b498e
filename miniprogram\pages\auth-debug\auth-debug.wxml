<!--pages/auth-debug/auth-debug.wxml-->
<view class="container">
  <view class="header">
    <text class="title">微信授权调试页面</text>
    <text class="subtitle">专门用于排查授权问题</text>
  </view>

  <!-- 环境信息 -->
  <view class="section">
    <view class="section-title">环境信息</view>
    <view class="info-item">
      <text class="label">基础库版本:</text>
      <text class="value">{{systemInfo.SDKVersion}}</text>
    </view>
    <view class="info-item">
      <text class="label">微信版本:</text>
      <text class="value">{{systemInfo.version}}</text>
    </view>
    <view class="info-item">
      <text class="label">AppID:</text>
      <text class="value">{{accountInfo.miniProgram.appId}}</text>
    </view>
    <view class="info-item">
      <text class="label">环境类型:</text>
      <text class="value">{{accountInfo.miniProgram.envVersion}}</text>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="section">
    <view class="section-title">功能测试</view>
    <button type="primary" bindtap="testGetUserProfile" class="test-btn">
      🔐 测试授权 (getUserProfile)
    </button>
    <button type="primary" bindtap="testDescLength" class="test-btn">
      📝 测试 desc 长度
    </button>
    <button type="default" bindtap="testBasicLogin" class="test-btn">
      🔑 测试基础登录 (wx.login)
    </button>
    <button type="warn" bindtap="showRealDeviceGuide" class="test-btn">
      📱 真机测试指南
    </button>
  </view>

  <!-- 用户信息显示 -->
  <view class="section" wx:if="{{hasUserInfo}}">
    <view class="section-title">获取到的用户信息</view>
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName}}</text>
        <text class="gender">性别: {{userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '未知'}}</text>
        <text class="location">地区: {{userInfo.country}} {{userInfo.province}} {{userInfo.city}}</text>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="section" wx:if="{{testResults}}">
    <view class="section-title">测试结果</view>
    <view class="result-item" wx:if="{{testResults.getUserProfile}}">
      <text class="result-label">getUserProfile:</text>
      <text class="result-value">{{testResults.getUserProfile}}</text>
    </view>
    <view class="result-item" wx:if="{{testResults.login}}">
      <text class="result-label">wx.login:</text>
      <text class="result-value">{{testResults.login}}</text>
    </view>
  </view>

  <!-- 调试日志 -->
  <view class="section">
    <view class="section-title">
      <text>调试日志</text>
      <view class="log-actions">
        <button size="mini" bindtap="clearLogs">清空</button>
        <button size="mini" bindtap="copyLogs">复制</button>
      </view>
    </view>
    <scroll-view class="log-container" scroll-y="true">
      <view class="log-item" wx:for="{{debugLogs}}" wx:key="index">
        {{item}}
      </view>
      <view class="log-empty" wx:if="{{debugLogs.length === 0}}">
        暂无日志
      </view>
    </scroll-view>
  </view>

  <!-- 重要提示 -->
  <view class="section tips">
    <view class="section-title">⚠️ 重要提示</view>
    <view class="tip-item important">
      <text class="tip-icon">🔥</text>
      <text class="tip-text">如果获取到"微信用户"和 is_demote: true，这是开发工具的模拟数据</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">📱</text>
      <text class="tip-text">要获取真实用户信息，必须在真机上测试</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">🎯</text>
      <text class="tip-text">点击"真机测试指南"查看详细步骤</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">⚡</text>
      <text class="tip-text">基础库版本需要 >= 2.10.4 才支持 getUserProfile</text>
    </view>
  </view>
</view>
