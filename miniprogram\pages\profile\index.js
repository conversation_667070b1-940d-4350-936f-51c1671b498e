// pages/profile/index.js
Page({
  data: {
    darkMode: false,
    isLoggedIn: false,
    userInfo: null,
    notifications: {
      tasks: true,
      water: true,
      sleep: false
    },
    userStats: {
      level: 7,
      petName: '小橙',
      petMood: '非常开心',
      companionDays: 18,
      completedTasks: 156,
      totalExp: 2340
    },
    achievements: [
      { name: '坚持达人', icon: '🏆', unlocked: true },
      { name: '健康之星', icon: '⭐', unlocked: true },
      { name: '早起鸟儿', icon: '🌅', unlocked: false },
      { name: '运动健将', icon: '💪', unlocked: true }
    ],
    topSpacing: 120
  },

  onLoad: function (options) {
    this.setTopSpacing();
    this.checkLoginStatus();
    this.loadUserData();
  },

  onShow: function () {
    this.checkLoginStatus();
    this.loadUserData();
  },

  // 设置顶部间距
  setTopSpacing: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    const capsuleInfo = wx.getMenuButtonBoundingClientRect();
    const topSpacing = capsuleInfo.top;
    
    this.setData({
      topSpacing: topSpacing
    });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    const isLoggedIn = app.isLoggedIn();
    const userInfo = app.getUserInfo();
    
    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo
    });
  },

  // 加载用户数据
  loadUserData: function() {
    const app = getApp();
    const petData = app.getPetData();
    if (petData) {
      this.setData({
        'userStats.level': petData.level,
        'userStats.petName': petData.name || '小橙',
        'userStats.companionDays': petData.totalDays || 18,
        'userStats.totalExp': petData.totalExperience || 2340
      });
    }
  },

  // 切换深色模式
  toggleDarkMode: function() {
    this.setData({
      darkMode: !this.data.darkMode
    });
  },

  // 切换通知设置
  toggleNotification: function(e) {
    const type = e.currentTarget.dataset.type;
    const key = `notifications.${type}`;
    this.setData({
      [key]: !this.data.notifications[type]
    });
  },

  // 编辑头像
  editAvatar: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 设置项点击
  onSettingTap: function(e) {
    const type = e.currentTarget.dataset.type;
    wx.showToast({
      title: `${type}功能开发中`,
      icon: 'none'
    });
  },

  // 微信登录 - 跳转到新版登录页面
  wxLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },



  // 退出登录
  logout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？退出后将无法同步数据。',
      success: (res) => {
        if (res.confirm) {
          const app = getApp();
          const success = app.logout();
          
          if (success) {
            this.setData({
              isLoggedIn: false,
              userInfo: null
            });
            
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '退出失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 跳转到聊天页面
  goToChat: function() {
    wx.switchTab({
      url: '/pages/chat/index'
    });
  },

  // 跳转到登录页面
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  }
})