/**
 * 任务管理器
 * 负责日常任务的管理、完成状态更新和奖励发放
 */
const { ErrorHandler } = require('./errorHandler')
const SecureStorage = require('./secureStorage')
const Logger = require('./logger')

class TaskManager {
  constructor() {
    this.storageKey = 'dailyTasks'
    this.taskTemplates = this.getTaskTemplates()
  }

  /**
   * 获取任务模板
   */
  getTaskTemplates() {
    return [
      {
        id: 'daily_walk',
        name: '每日步行',
        description: '完成8000步的步行目标',
        type: 'daily',
        reward: 20,
        icon: 'walk',
        target: 8000,
        unit: '步'
      },
      {
        id: 'drink_water',
        name: '喝水打卡',
        description: '喝足够的水保持健康',
        type: 'daily',
        reward: 15,
        icon: 'water',
        target: 8,
        unit: '杯'
      },
      {
        id: 'early_sleep',
        name: '早睡早起',
        description: '保持良好的作息习惯',
        type: 'daily',
        reward: 25,
        icon: 'sleep',
        target: 1,
        unit: '次'
      }
    ]
  }

  /**
   * 获取今日任务数据
   */
  getTodayTasks() {
    return ErrorHandler.safeExecuteSync(() => {
      const today = this.getTodayDateString()
      const stored = SecureStorage.getItem(this.storageKey, null, true)
      
      if (!stored || stored.date !== today) {
        Logger.info('任务数据过期或不存在，重置今日任务')
        return this.resetDailyTasks()
      }
      
      return stored.tasks
    }, this.resetDailyTasks(), 'getTodayTasks')
  }

  /**
   * 重置每日任务
   */
  resetDailyTasks() {
    const today = this.getTodayDateString()
    const tasks = this.taskTemplates.map(template => ({
      ...template,
      completed: false,
      progress: 0,
      completedAt: null,
      date: today
    }))
    
    const taskData = {
      date: today,
      tasks: tasks,
      totalReward: 0,
      completedCount: 0
    }
    
    this.saveTaskData(taskData)
    return tasks
  }

  /**
   * 保存任务数据
   */
  saveTaskData(taskData) {
    return ErrorHandler.safeExecuteSync(() => {
      const success = SecureStorage.setItem(this.storageKey, taskData, true)
      
      if (success) {
        Logger.debug('任务数据保存成功', { 
          date: taskData.date, 
          completed: taskData.completedCount,
          total: taskData.tasks.length 
        })
      }
      
      return success
    }, false, 'saveTaskData')
  }

  /**
   * 获取今日日期字符串
   */
  getTodayDateString() {
    return new Date().toDateString()
  }

  /**
   * 完成任务
   */
  completeTask(taskId) {
    try {
      const today = this.getTodayDateString()
      const taskData = wx.getStorageSync(this.storageKey)
      
      if (!taskData || taskData.date !== today) {
        throw new Error('任务数据不存在或已过期')
      }
      
      const task = taskData.tasks.find(t => t.id === taskId)
      if (!task) {
        throw new Error('任务不存在')
      }
      
      if (task.completed) {
        return {
          success: false,
          message: '任务已完成',
          reward: 0
        }
      }
      
      // 标记任务完成
      task.completed = true
      task.progress = task.target
      task.completedAt = new Date().getTime()
      
      // 更新统计
      taskData.completedCount = taskData.tasks.filter(t => t.completed).length
      taskData.totalReward += task.reward
      
      this.saveTaskData(taskData)
      
      // 发放奖励给宠物
      const PetDataManager = require('./petDataManager')
      const petManager = new PetDataManager()
      const result = petManager.addExperience(task.reward)
      
      return {
        success: true,
        message: `完成任务：${task.name}`,
        reward: task.reward,
        task: task,
        petData: result.petData,
        leveledUp: result.leveledUp
      }
    } catch (error) {
      console.error('完成任务失败:', error)
      return {
        success: false,
        message: error.message || '完成任务失败',
        reward: 0
      }
    }
  }

  /**
   * 更新任务进度
   */
  updateTaskProgress(taskId, progress) {
    try {
      const today = this.getTodayDateString()
      const taskData = wx.getStorageSync(this.storageKey)
      
      if (!taskData || taskData.date !== today) {
        return false
      }
      
      const task = taskData.tasks.find(t => t.id === taskId)
      if (!task || task.completed) {
        return false
      }
      
      task.progress = Math.min(progress, task.target)
      
      // 检查是否达到目标
      if (task.progress >= task.target && !task.completed) {
        return this.completeTask(taskId)
      }
      
      this.saveTaskData(taskData)
      return {
        success: true,
        task: task,
        autoCompleted: false
      }
    } catch (error) {
      console.error('更新任务进度失败:', error)
      return false
    }
  }

  /**
   * 获取任务完成统计
   */
  getTaskStats() {
    const tasks = this.getTodayTasks()
    const completed = tasks.filter(t => t.completed).length
    const total = tasks.length
    const totalReward = tasks.filter(t => t.completed).reduce((sum, t) => sum + t.reward, 0)
    
    return {
      completed,
      total,
      progress: total > 0 ? Math.round((completed / total) * 100) : 0,
      totalReward,
      tasks
    }
  }

  /**
   * 获取任务历史统计
   */
  getTaskHistory(days = 7) {
    try {
      const historyKey = 'taskHistory'
      const history = wx.getStorageSync(historyKey) || []
      
      return history.slice(-days).map(day => ({
        date: day.date,
        completed: day.completedCount || 0,
        total: day.tasks ? day.tasks.length : 0,
        reward: day.totalReward || 0
      }))
    } catch (error) {
      console.error('获取任务历史失败:', error)
      return []
    }
  }

  /**
   * 保存今日任务到历史记录
   */
  saveToHistory() {
    try {
      const today = this.getTodayDateString()
      const taskData = wx.getStorageSync(this.storageKey)
      
      if (!taskData || taskData.date !== today) {
        return false
      }
      
      const historyKey = 'taskHistory'
      const history = wx.getStorageSync(historyKey) || []
      
      // 检查今日记录是否已存在
      const existingIndex = history.findIndex(h => h.date === today)
      const todayRecord = {
        date: today,
        tasks: taskData.tasks,
        completedCount: taskData.completedCount,
        totalReward: taskData.totalReward,
        savedAt: new Date().getTime()
      }
      
      if (existingIndex >= 0) {
        history[existingIndex] = todayRecord
      } else {
        history.push(todayRecord)
      }
      
      // 只保留最近30天的记录
      if (history.length > 30) {
        history.splice(0, history.length - 30)
      }
      
      wx.setStorageSync(historyKey, history)
      return true
    } catch (error) {
      console.error('保存任务历史失败:', error)
      return false
    }
  }

  /**
   * 检查是否需要重置任务（跨日检查）
   */
  checkAndResetIfNeeded() {
    const today = this.getTodayDateString()
    const stored = wx.getStorageSync(this.storageKey)
    
    if (stored && stored.date !== today) {
      // 保存昨日记录到历史
      this.saveToHistory()
      // 重置今日任务
      return this.resetDailyTasks()
    }
    
    return this.getTodayTasks()
  }

  /**
   * 获取特定任务详情
   */
  getTaskById(taskId) {
    const tasks = this.getTodayTasks()
    return tasks.find(t => t.id === taskId) || null
  }

  /**
   * 批量更新多个任务进度
   */
  batchUpdateProgress(updates) {
    const results = []
    
    for (let update of updates) {
      const result = this.updateTaskProgress(update.taskId, update.progress)
      results.push({
        taskId: update.taskId,
        result: result
      })
    }
    
    return results
  }

  /**
   * 获取任务完成率趋势
   */
  getCompletionTrend(days = 7) {
    const history = this.getTaskHistory(days)
    
    return history.map(day => ({
      date: day.date,
      rate: day.total > 0 ? Math.round((day.completed / day.total) * 100) : 0,
      completed: day.completed,
      total: day.total
    }))
  }
}

module.exports = TaskManager