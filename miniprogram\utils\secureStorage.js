/**
 * 安全存储工具类
 * 提供安全的数据存储和读取方法
 */
const { <PERSON>rror<PERSON><PERSON><PERSON>, ErrorTypes } = require('./errorHandler')
const DataValidator = require('./dataValidator')

class SecureStorage {
  constructor() {
    this.encryptionKey = 'pet_app_key_2024'
  }

  /**
   * 安全设置存储数据
   */
  static setItem(key, data, validate = true) {
    const storage = new SecureStorage()
    return storage.setStorageItem(key, data, validate)
  }

  /**
   * 安全获取存储数据
   */
  static getItem(key, defaultValue = null, validate = true) {
    const storage = new SecureStorage()
    return storage.getStorageItem(key, defaultValue, validate)
  }

  /**
   * 设置存储项
   */
  setStorageItem(key, data, validate = true) {
    return ErrorHandler.safeExecuteSync(() => {
      // 数据验证
      if (validate) {
        if (!this.validateData(key, data)) {
          throw new Error(`数据验证失败: ${key}`)
        }
      }

      // 数据序列化和加密
      const serializedData = this.serializeData(data)
      const encryptedData = this.encryptData(serializedData)
      
      // 添加元数据
      const storageData = {
        data: encryptedData,
        timestamp: Date.now(),
        version: '1.0',
        checksum: this.generateChecksum(serializedData)
      }

      wx.setStorageSync(key, storageData)
      return true
    }, false, `setStorageItem:${key}`)
  }

  /**
   * 获取存储项
   */
  getStorageItem(key, defaultValue = null, validate = true) {
    return ErrorHandler.safeExecuteSync(() => {
      const storageData = wx.getStorageSync(key)
      
      if (!storageData) {
        return defaultValue
      }

      // 检查数据完整性
      if (!this.validateStorageData(storageData)) {
        console.warn(`存储数据损坏: ${key}`)
        return defaultValue
      }

      // 解密和反序列化
      const decryptedData = this.decryptData(storageData.data)
      const data = this.deserializeData(decryptedData)

      // 验证校验和
      if (storageData.checksum && storageData.checksum !== this.generateChecksum(decryptedData)) {
        console.warn(`数据校验失败: ${key}`)
        return defaultValue
      }

      // 数据验证
      if (validate && !this.validateData(key, data)) {
        console.warn(`数据格式验证失败: ${key}`)
        return defaultValue
      }

      return data
    }, defaultValue, `getStorageItem:${key}`)
  }

  /**
   * 验证存储数据结构
   */
  validateStorageData(storageData) {
    if (!storageData || typeof storageData !== 'object') return false
    if (!storageData.data || !storageData.timestamp || !storageData.version) return false
    
    // 检查时间戳合理性（不能是未来时间，不能太久远）
    const now = Date.now()
    const oneYearAgo = now - 365 * 24 * 60 * 60 * 1000
    if (storageData.timestamp > now || storageData.timestamp < oneYearAgo) return false
    
    return true
  }

  /**
   * 验证具体数据类型
   */
  validateData(key, data) {
    switch (key) {
      case 'petData':
        return DataValidator.validatePetData(data)
      case 'dailyTasks':
        return DataValidator.validateTaskData(data)
      case 'userSettings':
        return DataValidator.validateUserSettings(data)
      default:
        return true // 其他数据类型暂不验证
    }
  }

  /**
   * 序列化数据
   */
  serializeData(data) {
    try {
      return JSON.stringify(data)
    } catch (error) {
      throw new Error('数据序列化失败')
    }
  }

  /**
   * 反序列化数据
   */
  deserializeData(serializedData) {
    try {
      return JSON.parse(serializedData)
    } catch (error) {
      throw new Error('数据反序列化失败')
    }
  }

  /**
   * 简单加密（实际项目中应使用更强的加密算法）
   */
  encryptData(data) {
    // 这里使用简单的Base64编码作为示例
    // 实际项目中应该使用AES等加密算法
    try {
      return wx.arrayBufferToBase64(this.stringToArrayBuffer(data))
    } catch (error) {
      return data // 加密失败时返回原数据
    }
  }

  /**
   * 简单解密
   */
  decryptData(encryptedData) {
    try {
      return this.arrayBufferToString(wx.base64ToArrayBuffer(encryptedData))
    } catch (error) {
      return encryptedData // 解密失败时返回原数据
    }
  }

  /**
   * 字符串转ArrayBuffer
   */
  stringToArrayBuffer(str) {
    const buffer = new ArrayBuffer(str.length)
    const view = new Uint8Array(buffer)
    for (let i = 0; i < str.length; i++) {
      view[i] = str.charCodeAt(i)
    }
    return buffer
  }

  /**
   * ArrayBuffer转字符串
   */
  arrayBufferToString(buffer) {
    const view = new Uint8Array(buffer)
    let str = ''
    for (let i = 0; i < view.length; i++) {
      str += String.fromCharCode(view[i])
    }
    return str
  }

  /**
   * 生成数据校验和
   */
  generateChecksum(data) {
    let hash = 0
    if (data.length === 0) return hash.toString()
    
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return hash.toString()
  }

  /**
   * 清理过期数据
   */
  static cleanupExpiredData(maxAge = 30 * 24 * 60 * 60 * 1000) { // 默认30天
    return ErrorHandler.safeExecuteSync(() => {
      const storageInfo = wx.getStorageInfoSync()
      const now = Date.now()
      let cleanedCount = 0

      for (let key of storageInfo.keys) {
        try {
          const data = wx.getStorageSync(key)
          if (data && data.timestamp && (now - data.timestamp) > maxAge) {
            wx.removeStorageSync(key)
            cleanedCount++
          }
        } catch (error) {
          // 忽略单个key的错误
          continue
        }
      }

      console.log(`清理了 ${cleanedCount} 个过期数据项`)
      return cleanedCount
    }, 0, 'cleanupExpiredData')
  }

  /**
   * 获取存储使用情况
   */
  static getStorageUsage() {
    return ErrorHandler.safeExecuteSync(() => {
      const info = wx.getStorageInfoSync()
      return {
        currentSize: info.currentSize,
        limitSize: info.limitSize,
        keys: info.keys,
        usage: info.limitSize > 0 ? (info.currentSize / info.limitSize * 100).toFixed(2) : 0
      }
    }, null, 'getStorageUsage')
  }

  /**
   * 备份数据
   */
  static backupData(keys = []) {
    return ErrorHandler.safeExecuteSync(() => {
      const backup = {
        timestamp: Date.now(),
        version: '1.0',
        data: {}
      }

      const targetKeys = keys.length > 0 ? keys : ['petData', 'dailyTasks', 'userSettings']
      
      for (let key of targetKeys) {
        try {
          const data = SecureStorage.getItem(key)
          if (data !== null) {
            backup.data[key] = data
          }
        } catch (error) {
          console.warn(`备份数据失败: ${key}`, error)
        }
      }

      return backup
    }, null, 'backupData')
  }

  /**
   * 恢复数据
   */
  static restoreData(backup) {
    return ErrorHandler.safeExecuteSync(() => {
      if (!backup || !backup.data) {
        throw new Error('备份数据格式错误')
      }

      let restoredCount = 0
      for (let [key, data] of Object.entries(backup.data)) {
        try {
          if (SecureStorage.setItem(key, data)) {
            restoredCount++
          }
        } catch (error) {
          console.warn(`恢复数据失败: ${key}`, error)
        }
      }

      console.log(`恢复了 ${restoredCount} 个数据项`)
      return restoredCount
    }, 0, 'restoreData')
  }
}

module.exports = SecureStorage