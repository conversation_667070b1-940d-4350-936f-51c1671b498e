<!--components/pet-display/pet-display.wxml-->
<view class="pet-display-container">
  <!-- 宠物展示区域 -->
  <view class="pet-area">
    <!-- 等级标签 -->
    <view class="level-badge">{{levelText}}</view>
    
    <!-- 宠物形象 -->
    <view class="pet-image {{animationClass}}" bindtap="onPetTap">
      <image src="/images/pet-dragon.png" mode="aspectFit" class="pet-sprite"></image>
      
      <!-- 升级特效 -->
      <view class="levelup-effect" wx:if="{{showLevelUp}}">
        <text class="levelup-text">升级了！</text>
      </view>
    </view>
    
    <!-- 宠物信息 -->
    <view class="pet-info">
      <text class="pet-name">{{petName}}</text>
      <text class="pet-status">{{statusText}}</text>
    </view>
  </view>
  
  <!-- 互动提示 -->
  <view class="interaction-hint">
    <text class="hint-text">点击宠物进行互动</text>
  </view>
</view>