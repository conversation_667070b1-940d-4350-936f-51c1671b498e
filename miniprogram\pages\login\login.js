// pages/login/login.js
Page({
  data: {
    topSpacing: 120,
    avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
    nickName: '',
    hasUserInfo: false,
    isLoading: false
  },

  onLoad: function (options) {
    this.setTopSpacing();
    this.checkExistingLogin();
  },

  // 设置顶部间距
  setTopSpacing: function () {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    const capsuleInfo = wx.getMenuButtonBoundingClientRect();
    const topSpacing = capsuleInfo.top;

    this.setData({
      topSpacing: topSpacing
    });
  },

  // 检查已有登录信息
  checkExistingLogin: function () {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.nickName) {
        this.setData({
          avatarUrl: userInfo.avatarUrl,
          nickName: userInfo.nickName,
          hasUserInfo: true
        });
      }
    } catch (error) {
      console.log('检查登录信息失败:', error);
    }
  },

  // 选择头像
  onChooseAvatar: function (e) {
    const { avatarUrl } = e.detail;
    if (avatarUrl) {
      this.setData({
        avatarUrl: avatarUrl
      });
      this.checkUserInfo();
    }
  },

  // 昵称输入
  onNicknameInput: function (e) {
    const nickname = e.detail.value;
    this.setData({
      nickName: nickname
    });
    this.checkUserInfo();
  },

  // 检查用户信息完整性
  checkUserInfo: function () {
    const { avatarUrl, nickName } = this.data;
    const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
    const hasAvatar = avatarUrl && avatarUrl !== defaultAvatarUrl;
    const hasNickname = nickName && nickName.trim().length > 0;

    this.setData({
      hasUserInfo: hasAvatar && hasNickname
    });
  },

  // 完成登录
  completeLogin: function () {
    if (!this.data.hasUserInfo) {
      wx.showToast({
        title: '请完善头像和昵称',
        icon: 'none'
      });
      return;
    }

    this.setData({ isLoading: true });

    const { avatarUrl, nickName } = this.data;

    // 构造用户信息
    const userInfo = {
      nickName: nickName,
      avatarUrl: avatarUrl,
      loginTime: new Date().toISOString(),
      source: 'new-profile-login'
    };

    try {
      // 保存到本地存储
      wx.setStorageSync('userInfo', userInfo);

      // 保存到全局数据
      const app = getApp();
      if (app) {
        app.globalData.userInfo = userInfo;
        app.globalData.isLoggedIn = true;
      }

      // 尝试保存到云数据库
      this.saveToCloud(userInfo);

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 返回上一页或首页
      setTimeout(() => {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          wx.navigateBack();
        } else {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      }, 1500);

    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 保存到云数据库
  async saveToCloud(userInfo) {
    try {
      // 获取登录凭证
      const loginRes = await this.promisify(wx.login)();

      // 调用云函数获取 openid
      const cloudRes = await wx.cloud.callFunction({
        name: 'getOpenid',
        env: 'cloud1-8gino5ste2508c07',
        data: { code: loginRes.code }
      });

      const openid = cloudRes.result.openid;

      // 保存到数据库
      const db = wx.cloud.database({
        env: 'cloud1-8gino5ste2508c07'
      });

      const saveData = {
        ...userInfo,
        openid: openid,
        updateTime: new Date()
      };

      await db.collection('users').add({
        data: saveData
      });

      console.log('用户信息已保存到云数据库');

    } catch (error) {
      console.warn('云数据库保存失败:', error);
      // 本地保存仍然有效，不影响登录流程
    }
  },

  // Promise 化微信 API
  promisify: function (fn) {
    return (options = {}) => {
      return new Promise((resolve, reject) => {
        fn({
          ...options,
          success: resolve,
          fail: reject
        });
      });
    };
  },

  // 返回首页
  goHome: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
})