/**
 * 数据验证工具类
 * 提供各种数据验证方法
 */
class DataValidator {
  /**
   * 验证宠物属性值
   */
  static validatePetAttribute(value) {
    if (typeof value !== 'number') return false
    return value >= 0 && value <= 100
  }

  /**
   * 验证宠物等级
   */
  static validatePetLevel(level) {
    if (typeof level !== 'number') return false
    return level >= 1 && level <= 100
  }

  /**
   * 验证经验值
   */
  static validateExperience(exp) {
    if (typeof exp !== 'number') return false
    return exp >= 0
  }

  /**
   * 验证宠物名称
   */
  static validatePetName(name) {
    if (typeof name !== 'string') return false
    return name.length >= 1 && name.length <= 10
  }

  /**
   * 验证任务ID
   */
  static validateTaskId(taskId) {
    if (typeof taskId !== 'string') return false
    return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(taskId)
  }

  /**
   * 验证任务进度
   */
  static validateTaskProgress(progress, target) {
    if (typeof progress !== 'number' || typeof target !== 'number') return false
    return progress >= 0 && progress <= target
  }

  /**
   * 验证时间戳
   */
  static validateTimestamp(timestamp) {
    if (typeof timestamp !== 'number') return false
    const now = Date.now()
    const oneYearAgo = now - 365 * 24 * 60 * 60 * 1000
    return timestamp >= oneYearAgo && timestamp <= now + 24 * 60 * 60 * 1000
  }

  /**
   * 验证用户设置
   */
  static validateUserSettings(settings) {
    if (!settings || typeof settings !== 'object') return false
    
    const validKeys = ['taskReminder', 'waterReminder', 'sleepReminder', 'dataSync', 'theme', 'language']
    const booleanKeys = ['taskReminder', 'waterReminder', 'sleepReminder', 'dataSync']
    const stringKeys = ['theme', 'language']
    
    for (let key in settings) {
      if (!validKeys.includes(key)) return false
      
      if (booleanKeys.includes(key) && typeof settings[key] !== 'boolean') return false
      if (stringKeys.includes(key) && typeof settings[key] !== 'string') return false
    }
    
    return true
  }

  /**
   * 清理和验证输入数据
   */
  static sanitizeInput(input, type = 'string') {
    if (input === null || input === undefined) return null
    
    switch (type) {
      case 'string':
        return String(input).trim().slice(0, 100)
      case 'number':
        const num = Number(input)
        return isNaN(num) ? 0 : num
      case 'boolean':
        return Boolean(input)
      default:
        return input
    }
  }

  /**
   * 验证完整的宠物数据对象
   */
  static validatePetData(petData) {
    if (!petData || typeof petData !== 'object') return false
    
    // 必需字段检查
    const requiredFields = ['id', 'name', 'level', 'experience', 'maxExperience', 'attributes']
    for (let field of requiredFields) {
      if (!(field in petData)) return false
    }
    
    // 字段类型和值检查
    if (!this.validatePetName(petData.name)) return false
    if (!this.validatePetLevel(petData.level)) return false
    if (!this.validateExperience(petData.experience)) return false
    if (!this.validateExperience(petData.maxExperience)) return false
    
    // 属性检查
    if (!petData.attributes || typeof petData.attributes !== 'object') return false
    const requiredAttributes = ['health', 'energy', 'intimacy']
    for (let attr of requiredAttributes) {
      if (!(attr in petData.attributes)) return false
      if (!this.validatePetAttribute(petData.attributes[attr])) return false
    }
    
    // 时间戳检查（如果存在）
    if (petData.lastUpdateTime && !this.validateTimestamp(petData.lastUpdateTime)) return false
    if (petData.createdAt && !this.validateTimestamp(petData.createdAt)) return false
    
    return true
  }

  /**
   * 验证任务数据对象
   */
  static validateTaskData(taskData) {
    if (!taskData || typeof taskData !== 'object') return false
    
    // 检查日期
    if (!taskData.date || typeof taskData.date !== 'string') return false
    
    // 检查任务列表
    if (!Array.isArray(taskData.tasks)) return false
    
    for (let task of taskData.tasks) {
      if (!this.validateSingleTask(task)) return false
    }
    
    return true
  }

  /**
   * 验证单个任务对象
   */
  static validateSingleTask(task) {
    if (!task || typeof task !== 'object') return false
    
    const requiredFields = ['id', 'name', 'type', 'reward', 'completed']
    for (let field of requiredFields) {
      if (!(field in task)) return false
    }
    
    if (!this.validateTaskId(task.id)) return false
    if (typeof task.name !== 'string' || task.name.length === 0) return false
    if (typeof task.reward !== 'number' || task.reward < 0) return false
    if (typeof task.completed !== 'boolean') return false
    
    return true
  }
}

module.exports = DataValidator