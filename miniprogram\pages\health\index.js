// pages/health/index.js
const TaskManager = require('../../utils/taskManager')
const PetDataManager = require('../../utils/petDataManager')

Page({
  data: {
    topSpacing: 120,
    currentCategory: 'all',
    completedTasks: 0,
    totalTasks: 0,
    todayExp: 0,
    progressPercentage: 0,
    
    // 任务分类
    taskCategories: [
      { id: 'all', name: '全部', icon: '📋', count: 0 },
      { id: 'daily', name: '日常', icon: '📅', count: 0 },
      { id: 'health', name: '健康', icon: '❤️', count: 0 },
      { id: 'exercise', name: '运动', icon: '🏃', count: 0 },
      { id: 'habit', name: '习惯', icon: '⭐', count: 0 }
    ],
    
    // 所有任务
    allTasks: [],
    
    // 过滤后的任务
    filteredTasks: []
  },

  onLoad: function(options) {
    this.taskManager = new TaskManager()
    this.petDataManager = new PetDataManager()
    
    this.setTopSpacing()
    this.loadTasks()
  },

  onShow: function() {
    this.loadTasks()
  },

  // 设置顶部间距
  setTopSpacing: function() {
    const systemInfo = wx.getSystemInfoSync()
    const capsuleInfo = wx.getMenuButtonBoundingClientRect()
    const topSpacing = capsuleInfo.top + capsuleInfo.height + 20
    
    this.setData({
      topSpacing: topSpacing
    })
  },

  // 加载任务数据
  loadTasks: function() {
    try {
      // 获取今日任务
      const tasks = this.taskManager.getTodayTasks()
      
      // 计算统计数据
      const completedTasks = tasks.filter(task => task.completed).length
      const totalTasks = tasks.length
      const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
      const todayExp = tasks.reduce((sum, task) => {
        return sum + (task.completed ? task.reward : 0)
      }, 0)

      // 更新分类计数
      const updatedCategories = this.data.taskCategories.map(category => {
        if (category.id === 'all') {
          category.count = totalTasks - completedTasks
        } else {
          const categoryTasks = tasks.filter(task => 
            task.category === category.id && !task.completed
          )
          category.count = categoryTasks.length
        }
        return category
      })

      this.setData({
        allTasks: tasks,
        completedTasks: completedTasks,
        totalTasks: totalTasks,
        progressPercentage: progressPercentage,
        todayExp: todayExp,
        taskCategories: updatedCategories
      })

      // 应用当前分类过滤
      this.filterTasks()

    } catch (error) {
      console.error('加载任务失败:', error)
      wx.showToast({
        title: '加载任务失败',
        icon: 'none'
      })
    }
  },

  // 切换任务分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentCategory: category
    })
    this.filterTasks()
  },

  // 过滤任务
  filterTasks: function() {
    let filteredTasks = this.data.allTasks

    if (this.data.currentCategory !== 'all') {
      filteredTasks = this.data.allTasks.filter(task => 
        task.category === this.data.currentCategory
      )
    }

    this.setData({
      filteredTasks: filteredTasks
    })
  },

  // 任务完成处理
  onTaskComplete: function(e) {
    const { taskId, reward } = e.detail
    
    try {
      // 完成任务
      const earnedReward = this.taskManager.completeTask(taskId)
      
      if (earnedReward > 0) {
        // 更新宠物经验
        this.petDataManager.addExperience(earnedReward)
        
        // 显示奖励提示
        wx.showToast({
          title: `完成任务！获得${earnedReward}经验`,
          icon: 'success',
          duration: 2000
        })

        // 重新加载任务数据
        this.loadTasks()
        
        // 触发页面震动反馈
        wx.vibrateShort()
      }
      
    } catch (error) {
      console.error('完成任务失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 显示添加任务弹窗
  showAddTaskModal: function() {
    wx.showModal({
      title: '添加自定义任务',
      content: '此功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadTasks()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: '我正在用虚拟宠物养成健康习惯！',
      path: '/pages/health/index',
      imageUrl: '/images/share/health-tasks.png'
    }
  }
})