<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- Header -->
  <view class="header" style="padding-top: {{topSpacing}}px;">
    <view class="header-content">
      <view class="header-title">登录</view>
    </view>
  </view>

  <!-- Login Content -->
  <view class="login-content">
    <!-- User Info Display -->
    <view class="user-display">
      <image class="avatar" src="{{avatarUrl}}" wx:if="{{avatarUrl}}" mode="aspectFill"></image>
      <view wx:else class="avatar-placeholder">👤</view>
      <text class="nickname" wx:if="{{nickName}}">{{nickName}}</text>
      <text wx:else class="nickname-placeholder">未登录</text>
    </view>

    <!-- Login Button -->
    <button type="primary" bindtap="wxLogin">获取头像和昵称</button>
    
    <!-- Skip Login -->
    <view class="skip-login" bindtap="goHome">
      <text class="skip-text">暂不登录，直接体验</text>
    </view>
  </view>
</view>