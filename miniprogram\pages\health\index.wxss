/* pages/health/index.wxss */
page {
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

.health-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 20rpx 40rpx 40rpx;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #666;
  opacity: 0.8;
}

/* 进度概览 */
.progress-overview {
  padding: 0 40rpx 30rpx;
}

.overview-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 40rpx;
  padding: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.overview-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-divider {
  width: 2rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.1);
}

/* 进度条 */
.progress-bar-container {
  text-align: center;
}

.progress-bar {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 20rpx;
  height: 16rpx;
  margin-bottom: 15rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #66BB6A);
  border-radius: 20rpx;
  transition: width 0.8s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 任务分类 */
.task-categories {
  padding: 0 40rpx 30rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 30rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  min-width: 120rpx;
}

.category-item.active {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  transform: translateY(-4rpx);
}

.category-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.category-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: #FF5722;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

/* 任务列表区域 */
.task-list-section {
  padding: 0 40rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  opacity: 0.5;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  opacity: 0.8;
}

/* 浮动添加按钮 */
.fab-button {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4);
  transition: all 0.3s ease;
  z-index: 100;
}

.fab-button:active {
  transform: scale(0.95);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .health-page {
    padding-bottom: 100rpx;
  }
  
  .page-header {
    padding: 15rpx 30rpx 30rpx;
  }
  
  .header-title {
    font-size: 40rpx;
  }
  
  .header-subtitle {
    font-size: 24rpx;
  }
  
  .progress-overview {
    padding: 0 30rpx 25rpx;
  }
  
  .overview-card {
    padding: 25rpx;
  }
  
  .stat-number {
    font-size: 32rpx;
  }
  
  .task-categories {
    padding: 0 30rpx 25rpx;
  }
  
  .category-item {
    padding: 15rpx 25rpx;
    min-width: 100rpx;
  }
  
  .category-icon {
    font-size: 28rpx;
  }
  
  .category-name {
    font-size: 22rpx;
  }
  
  .task-list-section {
    padding: 0 30rpx;
  }
  
  .fab-button {
    width: 100rpx;
    height: 100rpx;
    bottom: 30rpx;
    right: 30rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.overview-card {
  animation: fadeInUp 0.6s ease-out;
}

.category-item {
  animation: slideInLeft 0.6s ease-out;
}

.category-item:nth-child(2) {
  animation-delay: 0.1s;
}

.category-item:nth-child(3) {
  animation-delay: 0.2s;
}

.category-item:nth-child(4) {
  animation-delay: 0.3s;
}

.category-item:nth-child(5) {
  animation-delay: 0.4s;
}