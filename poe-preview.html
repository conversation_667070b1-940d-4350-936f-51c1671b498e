<!DOCTYPE html><html><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;"><meta http-equiv="x-dns-prefetch-control" content="off"><meta name="x-poe-datastore-behavior" content="disabled"><meta name="x-poe-allow-downloads" content="true"><script src="https://puc.poecdn.net/standard.3ef2c256959faf5a756d.js"></script>
    <meta charset="utf-8">
    <title>Poe</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
  <script src="https://puc.poecdn.net/disableWebRTC.9710cebe07429a9e8e06.js"></script><script src="https://puc.poecdn.net/tw.b9024aecac666455e183.js"></script><script src="https://puc.poecdn.net/deps.faccd16000d314dc16d5.js"></script><script src="https://puc.poecdn.net/exports.b0f0f482cdeb5302b0b9.js"></script><script src="https://puc.poecdn.net/renderer.75c73ae6b4235f62945a.js"></script><script>Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }var _react = require('react'); var _react2 = _interopRequireDefault(_react);
var _lucidereact = require('lucide-react');

const PetApp = () => {
  const [currentTime, setCurrentTime] = _react.useState.call(void 0, new Date());
  const [activeTab, setActiveTab] = _react.useState.call(void 0, 'overview');
  const [petMood, setPetMood] = _react.useState.call(void 0, 'happy');

  _react.useEffect.call(void 0, () => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const petStats = {
    health: 85,
    energy: 72,
    happiness: 95,
    experience: 340,
    maxExp: 500,
    level: 8
  };

  const tasks = [
    { id: 1, name: '晨间冥想', description: '5分钟正念练习', reward: 25, completed: true, icon: _lucidereact.Sun, color: 'bg-yellow-500' },
    { id: 2, name: '健康饮水', description: '喝够8杯水', reward: 20, completed: true, icon: _lucidereact.Droplets, color: 'bg-blue-500' },
    { id: 3, name: '运动打卡', description: '30分钟有氧运动', reward: 35, completed: false, icon: _lucidereact.Activity, color: 'bg-green-500' },
    { id: 4, name: '早睡早起', description: '11点前入睡', reward: 30, completed: false, icon: _lucidereact.Moon, color: 'bg-purple-500' }
  ];

  const achievements = [
    { name: '健康达人', progress: 80 },
    { name: '运动之星', progress: 45 },
    { name: '时间管理师', progress: 90 }
  ];

  return (
    _react2.default.createElement('div', { className: "min-h-screen bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100"    ,}
      /* Header */
      , _react2.default.createElement('div', { className: "bg-white/80 backdrop-blur-sm border-b border-white/20 px-6 py-4 sticky top-0 z-10"        ,}
        , _react2.default.createElement('div', { className: "flex justify-between items-center"  ,}
          , _react2.default.createElement('div', null
            , _react2.default.createElement('h1', { className: "text-xl font-bold text-gray-800"  ,}, "我的成长伙伴")
            , _react2.default.createElement('p', { className: "text-sm text-gray-600" ,}, currentTime.toLocaleString())
          )
          , _react2.default.createElement('div', { className: "flex items-center space-x-2"  ,}
            , _react2.default.createElement('div', { className: "bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold"        ,}, "Lv."
              , petStats.level
            )
          )
        )
      )

      /* Pet Display */
      , _react2.default.createElement('div', { className: "px-6 py-6" ,}
        , _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-3xl p-6 shadow-lg border border-white/30"      ,}
          , _react2.default.createElement('div', { className: "text-center",}
            , _react2.default.createElement('div', { className: "relative mx-auto w-32 h-32 mb-4"    ,}
              , _react2.default.createElement('div', { className: "absolute inset-0 bg-gradient-to-br from-orange-400 to-pink-500 rounded-full animate-pulse"      ,})
              , _react2.default.createElement('div', { className: "absolute inset-2 bg-white rounded-full flex items-center justify-center"      ,}
                , _react2.default.createElement('div', { className: "text-4xl animate-bounce" ,}, "🦊")
              )
              , _react2.default.createElement('div', { className: "absolute -top-2 -right-2 bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold"            ,}
                , petStats.level
              )
            )
            , _react2.default.createElement('h2', { className: "text-2xl font-bold text-gray-800 mb-2"   ,}, "小橙")
            , _react2.default.createElement('p', { className: "text-gray-600 mb-4" ,}, "今天状态超棒！")

            /* Experience Bar */
            , _react2.default.createElement('div', { className: "bg-gray-200 rounded-full h-3 mb-4"   ,}
              , _react2.default.createElement('div', { 
                className: "bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000"      ,
                style: { width: `${(petStats.experience / petStats.maxExp) * 100}%` },}
)
            )
            , _react2.default.createElement('p', { className: "text-sm text-gray-600" ,}, petStats.experience, "/", petStats.maxExp, " EXP" )
          )
        )
      )

      /* Stats Cards */
      , _react2.default.createElement('div', { className: "px-6 mb-6" ,}
        , _react2.default.createElement('div', { className: "grid grid-cols-3 gap-4"  ,}
          , _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/30"      ,}
            , _react2.default.createElement(_lucidereact.Heart, { className: "w-8 h-8 text-red-500 mx-auto mb-2"    ,} )
            , _react2.default.createElement('div', { className: "text-2xl font-bold text-gray-800"  ,}, petStats.health)
            , _react2.default.createElement('div', { className: "text-xs text-gray-600" ,}, "健康值")
            , _react2.default.createElement('div', { className: "bg-red-200 rounded-full h-2 mt-2"   ,}
              , _react2.default.createElement('div', { className: "bg-red-500 h-2 rounded-full"  , style: { width: `${petStats.health}%` },})
            )
          )

          , _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/30"      ,}
            , _react2.default.createElement(_lucidereact.Zap, { className: "w-8 h-8 text-yellow-500 mx-auto mb-2"    ,} )
            , _react2.default.createElement('div', { className: "text-2xl font-bold text-gray-800"  ,}, petStats.energy)
            , _react2.default.createElement('div', { className: "text-xs text-gray-600" ,}, "活力值")
            , _react2.default.createElement('div', { className: "bg-yellow-200 rounded-full h-2 mt-2"   ,}
              , _react2.default.createElement('div', { className: "bg-yellow-500 h-2 rounded-full"  , style: { width: `${petStats.energy}%` },})
            )
          )

          , _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/30"      ,}
            , _react2.default.createElement(_lucidereact.Star, { className: "w-8 h-8 text-purple-500 mx-auto mb-2"    ,} )
            , _react2.default.createElement('div', { className: "text-2xl font-bold text-gray-800"  ,}, petStats.happiness)
            , _react2.default.createElement('div', { className: "text-xs text-gray-600" ,}, "开心值")
            , _react2.default.createElement('div', { className: "bg-purple-200 rounded-full h-2 mt-2"   ,}
              , _react2.default.createElement('div', { className: "bg-purple-500 h-2 rounded-full"  , style: { width: `${petStats.happiness}%` },})
            )
          )
        )
      )

      /* Tab Navigation */
      , _react2.default.createElement('div', { className: "px-6 mb-4" ,}
        , _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-2xl p-2 border border-white/30"     ,}
          , _react2.default.createElement('div', { className: "grid grid-cols-3 gap-2"  ,}
            , [
              { id: 'overview', label: '概览', icon: _lucidereact.Calendar },
              { id: 'tasks', label: '任务', icon: _lucidereact.Trophy },
              { id: 'achievements', label: '成就', icon: _lucidereact.Gift }
            ].map(tab => {
              const Icon = tab.icon;
              return (
                _react2.default.createElement('button', {
                  key: tab.id,
                  onClick: () => setActiveTab(tab.id),
                  className: `flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all ${
                    activeTab === tab.id 
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg' 
                      : 'text-gray-600 hover:bg-white/50'
                  }`,}

                  , _react2.default.createElement(Icon, { className: "w-4 h-4" ,} )
                  , _react2.default.createElement('span', { className: "text-sm font-medium" ,}, tab.label)
                )
              );
            })
          )
        )
      )

      /* Content Area */
      , _react2.default.createElement('div', { className: "px-6 pb-8" ,}
        , activeTab === 'tasks' && (
          _react2.default.createElement('div', { className: "space-y-4",}
            , _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-white/30"     ,}
              , _react2.default.createElement('div', { className: "flex justify-between items-center mb-4"   ,}
                , _react2.default.createElement('h3', { className: "text-lg font-bold text-gray-800"  ,}, "今日任务")
                , _react2.default.createElement('span', { className: "text-sm text-gray-600" ,}, tasks.filter(t => t.completed).length, "/", tasks.length)
              )

              , tasks.map(task => {
                const Icon = task.icon;
                return (
                  _react2.default.createElement('div', { key: task.id, className: "flex items-center space-x-4 p-3 mb-3 bg-white/40 rounded-xl border border-white/20"        ,}
                    , _react2.default.createElement('div', { className: `p-2 rounded-full ${task.color}`,}
                      , _react2.default.createElement(Icon, { className: "w-5 h-5 text-white"  ,} )
                    )
                    , _react2.default.createElement('div', { className: "flex-1",}
                      , _react2.default.createElement('h4', { className: "font-semibold text-gray-800" ,}, task.name)
                      , _react2.default.createElement('p', { className: "text-sm text-gray-600" ,}, task.description)
                    )
                    , _react2.default.createElement('div', { className: "text-right",}
                      , _react2.default.createElement('div', { className: "text-sm font-semibold text-orange-600"  ,}, "+", task.reward, " EXP" )
                      , _react2.default.createElement('div', { className: `w-6 h-6 rounded-full mt-1 ${
                        task.completed ? 'bg-green-500' : 'bg-gray-300'
                      } flex items-center justify-center`,}
                        , task.completed && _react2.default.createElement('span', { className: "text-white text-xs" ,}, "✓")
                      )
                    )
                  )
                );
              })
            )
          )
        )

        , activeTab === 'achievements' && (
          _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-white/30"     ,}
            , _react2.default.createElement('h3', { className: "text-lg font-bold text-gray-800 mb-4"   ,}, "成就进度")
            , achievements.map((achievement, index) => (
              _react2.default.createElement('div', { key: index, className: "mb-4 p-3 bg-white/40 rounded-xl border border-white/20"     ,}
                , _react2.default.createElement('div', { className: "flex justify-between items-center mb-2"   ,}
                  , _react2.default.createElement('span', { className: "font-semibold text-gray-800" ,}, achievement.name)
                  , _react2.default.createElement('span', { className: "text-sm text-gray-600" ,}, achievement.progress, "%")
                )
                , _react2.default.createElement('div', { className: "bg-gray-200 rounded-full h-2"  ,}
                  , _react2.default.createElement('div', { 
                    className: "bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-1000"      ,
                    style: { width: `${achievement.progress}%` },}
)
                )
              )
            ))
          )
        )

        , activeTab === 'overview' && (
          _react2.default.createElement('div', { className: "space-y-4",}
            , _react2.default.createElement('div', { className: "bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-white/30"     ,}
              , _react2.default.createElement('h3', { className: "text-lg font-bold text-gray-800 mb-4"   ,}, "今日摘要")
              , _react2.default.createElement('div', { className: "grid grid-cols-2 gap-4"  ,}
                , _react2.default.createElement('div', { className: "text-center p-3 bg-white/40 rounded-xl"   ,}
                  , _react2.default.createElement('div', { className: "text-2xl font-bold text-green-600"  ,}, "12")
                  , _react2.default.createElement('div', { className: "text-sm text-gray-600" ,}, "已完成任务")
                )
                , _react2.default.createElement('div', { className: "text-center p-3 bg-white/40 rounded-xl"   ,}
                  , _react2.default.createElement('div', { className: "text-2xl font-bold text-purple-600"  ,}, "340")
                  , _react2.default.createElement('div', { className: "text-sm text-gray-600" ,}, "今日经验")
                )
              )
            )
          )
        )
      )
    )
  );
};

exports. default = PetApp;
</script></head>
  <body>
  

<div id="preview-app"></div></body></html>