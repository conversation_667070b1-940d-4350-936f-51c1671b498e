// components/pet-display/pet-display.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    petData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          this.updatePetDisplay(newVal);
        }
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    animationClass: '',
    statusText: '正在休息中...',
    levelText: 'Lv.1',
    petName: '小宠物',
    showLevelUp: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新宠物显示
     */
    updatePetDisplay(petData) {
      const statusTexts = {
        happy: '心情愉快',
        normal: '状态正常', 
        tired: '有点疲惫',
        hungry: '肚子饿了',
        sleepy: '想要睡觉'
      };

      this.setData({
        levelText: `Lv.${petData.level || 1}`,
        petName: petData.name || '小宠物',
        statusText: statusTexts[petData.status] || '正在休息中...'
      });

      // 根据宠物状态更新动画
      this.updatePetAnimation(petData);
    },

    /**
     * 更新宠物动画效果
     */
    updatePetAnimation(petData) {
      let animationClass = '';
      
      // 根据健康值和心情状态决定动画
      if (petData.attributes) {
        const { health, energy, intimacy } = petData.attributes;
        
        if (health > 80 && energy > 60) {
          animationClass = 'pet-happy';
        } else if (health < 30 || energy < 20) {
          animationClass = 'pet-tired';
        } else if (intimacy > 80) {
          animationClass = 'pet-excited';
        } else {
          animationClass = 'pet-normal';
        }
      }

      this.setData({
        animationClass: animationClass
      });
    },

    /**
     * 宠物点击交互
     */
    onPetTap() {
      // 播放点击动画
      this.setData({
        animationClass: 'pet-tap'
      });

      // 增加亲密度
      this.triggerEvent('petInteraction', {
        type: 'tap',
        intimacyIncrease: 1
      });

      // 恢复正常动画
      setTimeout(() => {
        this.updatePetAnimation(this.data.petData);
      }, 500);

      // 显示互动反馈
      wx.showToast({
        title: '宠物很开心！',
        icon: 'none',
        duration: 1000
      });
    },

    /**
     * 显示升级动画
     */
    showLevelUpAnimation() {
      this.setData({
        showLevelUp: true,
        animationClass: 'pet-levelup'
      });

      setTimeout(() => {
        this.setData({
          showLevelUp: false
        });
        this.updatePetAnimation(this.data.petData);
      }, 2000);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
      if (this.data.petData && Object.keys(this.data.petData).length > 0) {
        this.updatePetDisplay(this.data.petData);
      }
    }
  }
});