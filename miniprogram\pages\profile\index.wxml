<!--pages/profile/index.wxml-->
<view class="page-container {{darkMode ? 'dark-mode' : ''}}">
  <!-- Header -->
  <view class="header" style="padding-top: {{topSpacing}}px;">
    <view class="header-content">
      <view class="header-title">个人中心</view>
      <view class="dark-mode-toggle" bindtap="toggleDarkMode">
        <text class="toggle-icon">{{darkMode ? '☀️' : '🌙'}}</text>
      </view>
    </view>
  </view>

  <!-- User Profile Card -->
  <view class="profile-card">
    <!-- 已登录状态 -->
    <view wx:if="{{isLoggedIn}}" class="profile-header">
      <view class="avatar-container">
        <image wx:if="{{userInfo.avatarUrl}}" class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view wx:else class="avatar">👤</view>
        <view class="edit-button" bindtap="editAvatar">
          <text class="edit-icon">✏️</text>
        </view>
      </view>
      <view class="user-info">
        <view class="username">{{userInfo.nickName || '微信用户'}}</view>
        <view class="user-level">
          <text class="crown-icon">👑</text>
          <text class="level-text">Lv.{{userStats.level}} 养宠达人</text>
        </view>
        <view class="login-time">登录时间: {{userInfo.loginTime ? userInfo.loginTime.slice(0,10) : ''}}</view>
      </view>
    </view>
    
    <!-- 未登录状态 -->
    <view wx:else class="login-prompt">
      <view class="login-avatar">
        <text class="login-icon">🔐</text>
      </view>
      <view class="login-info">
        <view class="login-title">未登录</view>
        <view class="login-desc">登录后可同步数据</view>
      </view>
      <view class="login-buttons">
        <button type="primary" bindtap="wxLogin">获取头像和昵称</button>
        <button type="default" bindtap="goToLogin">详细登录</button>
      </view>
    </view>

    <!-- Stats Grid - 只在登录时显示 -->
    <view wx:if="{{isLoggedIn}}" class="stats-grid">
      <view class="stat-item">
        <view class="stat-value companion-days">{{userStats.companionDays}}</view>
        <view class="stat-label">陪伴天数</view>
      </view>
      <view class="stat-item">
        <view class="stat-value completed-tasks">{{userStats.completedTasks}}</view>
        <view class="stat-label">完成任务</view>
      </view>
      <view class="stat-item">
        <view class="stat-value total-exp">{{userStats.totalExp}}</view>
        <view class="stat-label">总经验</view>
      </view>
    </view>

    <!-- Achievements - 只在登录时显示 -->
    <view wx:if="{{isLoggedIn}}" class="achievements">
      <view wx:for="{{achievements}}" wx:key="name" 
            class="achievement-badge {{item.unlocked ? 'unlocked' : 'locked'}}">
        <text>{{item.icon}}</text>
      </view>
    </view>
  </view>

  <!-- Pet Info Card -->
  <view class="pet-card">
    <view class="pet-header">
      <view class="pet-title">🦊 我的宠物</view>
      <text class="chevron-right">›</text>
    </view>
    <view class="pet-info">
      <view class="pet-avatar">🦊</view>
      <view class="pet-details">
        <view class="pet-name">{{userStats.petName}} · Lv.{{userStats.level}}</view>
        <view class="pet-mood">心情: {{userStats.petMood}}</view>
      </view>
      <view class="chat-icon" bindtap="goToChat">
        <text class="chat-emoji">💬</text>
      </view>
    </view>
  </view>

  <!-- Settings Sections -->
  <view class="settings-section">
    <!-- Notifications -->
    <view class="setting-card">
      <view class="setting-title">📱 通知设置</view>
      <view class="setting-item">
        <view class="setting-left">
          <text class="setting-icon">🎯</text>
          <text class="setting-label">任务提醒</text>
        </view>
        <view class="toggle-switch {{notifications.tasks ? 'active' : ''}}" 
              bindtap="toggleNotification" data-type="tasks">
          <view class="toggle-thumb"></view>
        </view>
      </view>
      <view class="setting-item">
        <view class="setting-left">
          <text class="setting-icon">💧</text>
          <text class="setting-label">饮水提醒</text>
        </view>
        <view class="toggle-switch {{notifications.water ? 'active' : ''}}" 
              bindtap="toggleNotification" data-type="water">
          <view class="toggle-thumb"></view>
        </view>
      </view>
      <view class="setting-item">
        <view class="setting-left">
          <text class="setting-icon">🌙</text>
          <text class="setting-label">睡眠提醒</text>
        </view>
        <view class="toggle-switch {{notifications.sleep ? 'active' : ''}}" 
              bindtap="toggleNotification" data-type="sleep">
          <view class="toggle-thumb"></view>
        </view>
      </view>
    </view>

    <!-- App Settings -->
    <view class="setting-card">
      <view class="setting-title">⚙️ 应用设置</view>
      <view class="setting-item" bindtap="onSettingTap" data-type="主题设置">
        <view class="setting-left">
          <text class="setting-icon">🎨</text>
          <text class="setting-label">主题设置</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">{{darkMode ? '深色模式' : '浅色模式'}}</text>
          <text class="chevron-right">›</text>
        </view>
      </view>
      <view class="setting-item" bindtap="onSettingTap" data-type="语言设置">
        <view class="setting-left">
          <text class="setting-icon">🌐</text>
          <text class="setting-label">语言设置</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">简体中文</text>
          <text class="chevron-right">›</text>
        </view>
      </view>
      <view class="setting-item" bindtap="onSettingTap" data-type="存储管理">
        <view class="setting-left">
          <text class="setting-icon">📱</text>
          <text class="setting-label">存储管理</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">16.0MB</text>
          <text class="chevron-right">›</text>
        </view>
      </view>
      <view class="setting-item" bindtap="onSettingTap" data-type="隐私政策">
        <view class="setting-left">
          <text class="setting-icon">🛡️</text>
          <text class="setting-label">隐私政策</text>
        </view>
        <view class="setting-right">
          <text class="chevron-right">›</text>
        </view>
      </view>
    </view>

    <!-- About & Support -->
    <view class="setting-card">
      <view class="setting-title">ℹ️ 关于</view>
      <view class="setting-item" bindtap="onSettingTap" data-type="检查更新">
        <view class="setting-left">
          <text class="setting-icon">ℹ️</text>
          <text class="setting-label">检查更新</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">v1.0.0</text>
          <text class="chevron-right">›</text>
        </view>
      </view>
      <view class="setting-item" bindtap="onSettingTap" data-type="关于我们">
        <view class="setting-left">
          <text class="setting-icon">👥</text>
          <text class="setting-label">关于我们</text>
        </view>
        <view class="setting-right">
          <text class="chevron-right">›</text>
        </view>
      </view>
      <view class="setting-item" bindtap="onSettingTap" data-type="意见反馈">
        <view class="setting-left">
          <text class="setting-icon">💬</text>
          <text class="setting-label">意见反馈</text>
        </view>
        <view class="setting-right">
          <text class="chevron-right">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Logout Button - 只在登录时显示 -->
  <view wx:if="{{isLoggedIn}}" class="logout-section">
    <view class="logout-button" bindtap="logout">
      <text class="logout-icon">🚪</text>
      <text class="logout-text">退出登录</text>
    </view>
  </view>
</view>