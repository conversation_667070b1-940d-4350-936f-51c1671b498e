# 虚拟宠物养成小程序

## 功能特性

- ✅ 微信登录（获取头像和昵称）
- ✅ 云函数获取openid
- ✅ 用户信息数据库存储
- ✅ 虚拟宠物养成系统
- ✅ 日常任务系统
- ✅ 属性管理系统

## 部署步骤

### 1. 云开发环境配置

1. 在微信开发者工具中开通云开发
2. 创建云环境，获取环境ID
3. 修改 `miniprogram/app.js` 中的环境ID：
   ```javascript
   wx.cloud.init({
     env: 'your-cloud-env-id', // 替换为你的云环境ID
     traceUser: true,
   });
   ```

### 2. 部署云函数

1. 右键点击 `cloudfunctions/getOpenid` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 3. 数据库配置

云函数会自动创建 `users` 集合，无需手动创建。

用户数据结构：
```json
{
  "_id": "xxx",
  "_openid": "o_xxx",
  "nickName": "用户昵称",
  "avatarUrl": "头像URL",
  "gender": 1,
  "province": "省份",
  "city": "城市",
  "country": "国家",
  "language": "zh_CN",
  "createTime": "创建时间",
  "lastLoginTime": "最后登录时间",
  "role": "user",
  "phone": ""
}
```

### 4. 小程序配置

1. 修改 `project.config.json` 中的 `appid`
2. 在微信公众平台配置服务器域名
3. 添加云开发相关域名到合法域名列表

## 使用说明

### 登录流程

1. 用户点击"获取头像和昵称"按钮
2. 弹出授权弹窗，用户确认授权
3. 获取用户信息并调用云函数获取openid
4. 将用户信息保存到云数据库
5. 登录成功，显示用户头像和昵称

### 主要页面

- **宠物主页**：显示宠物状态和属性
- **健康任务**：日常任务列表和完成状态
- **宠物聊天**：与虚拟宠物互动
- **健康数据**：数据统计和分析
- **个人中心**：用户信息和设置

## 技术栈

- 微信小程序原生框架
- 微信云开发
- 云函数
- 云数据库

## 注意事项

1. 确保小程序基础库版本 >= 2.19.4
2. 需要开通微信云开发服务
3. 用户授权获取头像昵称需要用户主动点击触发
4. 云函数需要正确部署才能获取openid
5. 数据库权限设置为"仅创建者可读写"

## 开发调试

1. 在微信开发者工具中打开项目
2. 确保云开发环境已开通
3. 部署云函数
4. 编译运行小程序
5. 测试登录功能

## 常见问题

**Q: 云函数调用失败？**
A: 检查云环境ID是否正确，云函数是否正确部署

**Q: 用户信息获取失败？**
A: 确保用户主动点击授权按钮，不能自动调用

**Q: 数据库写入失败？**
A: 检查数据库权限设置和云函数权限