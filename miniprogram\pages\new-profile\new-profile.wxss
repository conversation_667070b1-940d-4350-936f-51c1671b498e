/* pages/new-profile/new-profile.wxss */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 用户信息区域 */
.user-info-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 头像选择 */
.avatar-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.avatar-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 6rpx solid #e0e0e0;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.avatar-btn:active .avatar {
  transform: scale(0.95);
  border-color: #667eea;
}

.avatar-tip {
  font-size: 24rpx;
  color: #666;
}

/* 昵称输入 */
.nickname-section {
  margin-bottom: 30rpx;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.nickname-input:focus {
  border-color: #667eea;
}

/* 状态显示 */
.info-status {
  text-align: center;
  padding: 20rpx;
  border-radius: 10rpx;
  background: #f5f5f5;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
}

.status-text.complete {
  color: #4caf50;
}

.status-text.incomplete {
  color: #ff9800;
}

/* 操作按钮 */
.action-section {
  margin-bottom: 30rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.action-btn.disabled {
  opacity: 0.5;
}

.action-btn:active {
  transform: scale(0.98);
}

/* 优势说明 */
.advantage-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.advantage-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.advantage-list {
  padding-left: 20rpx;
}

.advantage-item {
  display: block;
  font-size: 28rpx;
  color: #555;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

/* 调试日志 */
.debug-section {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  color: white;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.debug-title {
  font-size: 32rpx;
  font-weight: bold;
}

.clear-btn {
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.debug-logs {
  height: 400rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-item {
  font-size: 24rpx;
  line-height: 1.4;
  margin-bottom: 10rpx;
  font-family: 'Courier New', monospace;
  word-break: break-all;
}
