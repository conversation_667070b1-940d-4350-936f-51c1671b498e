/* chat.wxss */
page {
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
  height: 100vh;
}

.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #c7d2fe 0%, #f3e8ff 50%, #fce7f3 100%);
  background-attachment: fixed;
}

/* 聊天头部 */
.chat-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 20rpx 40rpx;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.back-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.9);
}

.back-icon {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.pet-avatar-small {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #fb923c, #ec4899);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-emoji-small {
  font-size: 40rpx;
}

.pet-info {
  flex: 1;
}

.pet-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.pet-status {
  font-size: 24rpx;
  color: #4CAF50;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-item {
  display: flex;
  align-items: flex-end;
  margin-bottom: 30rpx;
  gap: 20rpx;
}

.message-user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-emoji {
  font-size: 30rpx;
}

.message-bubble {
  max-width: 500rpx;
  padding: 20rpx 25rpx;
  border-radius: 25rpx;
  position: relative;
}

.bubble-pet {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.bubble-user {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.message-text {
  font-size: 28rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

.message-time {
  font-size: 20rpx;
  opacity: 0.7;
  margin-top: 8rpx;
  text-align: right;
}

.bubble-pet .message-time {
  color: #666;
}

.bubble-user .message-time {
  color: rgba(255, 255, 255, 0.8);
}

/* 输入区域 */
.chat-input-area {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50rpx;
  padding: 10rpx 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.message-input {
  flex: 1;
  font-size: 28rpx;
  padding: 15rpx 0;
  background: transparent;
  border: none;
  outline: none;
}

.send-button {
  width: 60rpx;
  height: 60rpx;
  background: #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-active {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.send-icon {
  font-size: 24rpx;
  color: white;
}

/* 动画效果 */
.message-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}