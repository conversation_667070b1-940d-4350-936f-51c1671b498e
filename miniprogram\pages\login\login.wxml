<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- Header -->
  <view class="header" style="padding-top: {{topSpacing}}px;">
    <view class="header-content">
      <view class="header-title">完善个人信息</view>
    </view>
  </view>

  <!-- Login Content -->
  <view class="login-content">
    <!-- 头像选择 -->
    <view class="avatar-section">
      <text class="section-title">选择头像</text>
      <button class="avatar-btn" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image class="avatar" src="{{avatarUrl}}" mode="aspectFill"></image>
        <view class="avatar-tip">点击选择头像</view>
      </button>
    </view>

    <!-- 昵称输入 -->
    <view class="nickname-section">
      <text class="section-title">输入昵称</text>
      <input
        class="nickname-input"
        type="nickname"
        placeholder="请输入昵称"
        value="{{nickName}}"
        bind:input="onNicknameInput"
        maxlength="20"
      />
    </view>

    <!-- 完成按钮 -->
    <button
      type="primary"
      class="complete-btn {{hasUserInfo ? '' : 'disabled'}}"
      bindtap="completeLogin"
      disabled="{{!hasUserInfo || isLoading}}"
      loading="{{isLoading}}"
    >
      {{isLoading ? '登录中...' : '完成登录'}}
    </button>

    <!-- Skip Login -->
    <view class="skip-login" bindtap="goHome">
      <text class="skip-text">暂不登录，直接体验</text>
    </view>

    <!-- 说明文字 -->
    <view class="login-tips">
      <text class="tip-item">✅ 用户主动选择，体验更好</text>
      <text class="tip-item">✅ 获取真实头像和昵称</text>
      <text class="tip-item">✅ 微信官方推荐方案</text>
    </view>
  </view>
</view>